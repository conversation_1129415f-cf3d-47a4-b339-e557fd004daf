# KiotViet MCP Server - Architecture Guide

## 🏗️ New Project Structure

The project has been restructured following Python best practices and clean architecture principles:

```
albatross-kiotviet-mcp/
├── src/albatross_kiotviet_mcp/          # Main package
│   ├── __init__.py                      # Package exports
│   ├── config.py                        # Configuration management
│   ├── auth.py                          # Authentication & token management
│   ├── client.py                        # KiotViet API client
│   ├── core/                            # 🧠 Business Logic Layer
│   │   ├── __init__.py
│   │   ├── invoices.py                  # Invoice business logic
│   │   └── categories.py                # Category business logic
│   ├── tools/                           # 🔧 MCP Tools Layer (Individual Files)
│   │   ├── __init__.py                  # Tool registration coordination
│   │   ├── get_invoices_by_date_tool.py        # Invoice retrieval tool
│   │   ├── get_invoice_statistics_tool.py      # Invoice statistics tool
│   │   ├── get_categories_tool.py              # Category retrieval tool
│   │   ├── get_all_categories_tool.py          # All categories tool
│   │   ├── build_category_tree_tool.py         # Category tree building tool
│   │   └── search_categories_tool.py           # Category search tool
│   └── server.py                        # 🚀 Main MCP server
├── tests/                               # 🧪 Test Layer
│   ├── __init__.py
│   ├── conftest.py                      # Pytest configuration
│   ├── unit/                            # Unit tests
│   │   ├── __init__.py
│   │   ├── test_invoices.py             # Test invoice logic
│   │   ├── test_categories.py           # Test category logic
│   │   └── tools/                       # Individual tool tests
│   │       ├── __init__.py
│   │       ├── test_get_invoices_by_date_tool.py
│   │       ├── test_get_invoice_statistics_tool.py
│   │       ├── test_get_categories_tool.py
│   │       ├── test_get_all_categories_tool.py
│   │       ├── test_build_category_tree_tool.py
│   │       └── test_search_categories_tool.py
│   └── integration/                     # Integration tests
│       ├── __init__.py
│       └── test_mcp_tools.py            # Test MCP tools
├── examples/                            # 📚 Examples & Manual Tests
│   └── test_manual.py                   # Manual testing script
├── test_invoices.py                     # Legacy test (updated)
├── test_unit_invoices.py                # Legacy test (updated)
└── ARCHITECTURE.md                      # This file
```

## 🎯 Architecture Principles

### 1. Separation of Concerns
- **Core Layer**: Pure business logic, no MCP dependencies
- **Tools Layer**: MCP tool wrappers, thin delegation layer
- **Server Layer**: Tool registration and server initialization

### 2. Dependency Direction
```
Server → Tools → Core → Client → API
```

### 3. Testing Strategy
- **Unit Tests**: Test core business logic directly
- **Integration Tests**: Test tools with real API calls
- **Manual Tests**: Examples and demonstrations

## 🧠 Core Layer (`src/albatross_kiotviet_mcp/core/`)

Contains pure business logic services that can be tested independently:

### InvoiceService (`invoices.py`)
- `get_invoices_by_date_range()`: Retrieve and enhance invoice data
- `calculate_invoice_statistics()`: Calculate invoice statistics
- `_format_date_range()`: Date formatting and validation
- `_enhance_invoice_result()`: Add summary information

### CategoryService (`categories.py`)
- `get_categories()`: Retrieve categories with validation
- `get_all_categories()`: Paginate through all categories
- `build_category_tree()`: Build hierarchical structure
- `search_categories()`: Search functionality

## 🔧 Tools Layer (`src/albatross_kiotviet_mcp/tools/`)

Individual tool files that register MCP tools and delegate to core services:

### Individual Tool Files
- `get_invoices_by_date_tool.py`: Invoice retrieval tool
- `get_invoice_statistics_tool.py`: Invoice statistics tool
- `get_categories_tool.py`: Category retrieval tool
- `get_all_categories_tool.py`: All categories tool
- `build_category_tree_tool.py`: Category tree building tool
- `search_categories_tool.py`: Category search tool

### Each Tool File Contains:
- **Service getter function**: Lazy-loaded singleton service instance
- **Tool registration function**: FastMCP tool decorator and implementation
- **Testing access function**: Direct service access for unit testing
- **Comprehensive documentation**: Type hints and detailed docstrings

## 🧪 Testing Strategy

### Unit Tests (`tests/unit/`)
Test individual services directly:

```python
from src.albatross_kiotviet_mcp.core.invoices import InvoiceService

# Test business logic directly
service = InvoiceService(mock_client)
result = await service.get_invoices_by_date_range("2025-07-20")
```

### Integration Tests (`tests/integration/`)
Test tools with real API calls:

```python
from src.albatross_kiotviet_mcp.tools.invoice_tools import get_invoice_service_for_testing

# Test with real API
service = get_invoice_service_for_testing()
result = await service.get_invoices_by_date_range("2025-07-20")
```

### Manual Tests (`examples/`)
Demonstrate usage and run comprehensive tests:

```bash
python examples/test_manual.py
```

## 🚀 Usage Examples

### For Testing (Direct Service Access)
```python
# Import service for testing
from src.albatross_kiotviet_mcp.tools.invoice_tools import get_invoice_service_for_testing

# Use directly
service = get_invoice_service_for_testing()
result = await service.get_invoices_by_date_range("2025-07-20")
```

### For MCP Client Usage
```python
# MCP tools are automatically registered
# Use through MCP protocol:
# - get_invoices_by_date
# - get_invoice_statistics
# - get_categories
# - get_all_categories
# - build_category_tree
# - search_categories
```

### For Development
```python
# Add new business logic in core/
class InvoiceService:
    async def new_feature(self):
        # Pure business logic here
        pass

# Add MCP tool in tools/
@server.tool(name="new_tool")
async def new_tool():
    service = get_invoice_service()
    return await service.new_feature()
```

## 🔄 Migration Benefits

### Before (Monolithic)
- ❌ Business logic mixed with MCP concerns
- ❌ Hard to test (FunctionTool wrapper issues)
- ❌ Difficult to reuse logic
- ❌ Poor separation of concerns

### After (Layered Architecture)
- ✅ Clean separation of business logic and MCP tools
- ✅ Easy testing with direct service access
- ✅ Reusable business logic services
- ✅ Clear, maintainable code structure
- ✅ Better error handling and validation
- ✅ Comprehensive test coverage

## 🛠️ Development Workflow

1. **Add Business Logic**: Implement in `core/` services
2. **Add MCP Tools**: Create wrappers in `tools/`
3. **Add Tests**: Unit tests in `tests/unit/`, integration in `tests/integration/`
4. **Register Tools**: Update `server.py` to register new tools
5. **Document**: Update README and examples

## 📚 Running Tests

```bash
# Unit tests (fast, no API calls)
pytest tests/unit/

# Integration tests (requires API credentials)
pytest tests/integration/

# All tests
pytest

# Manual testing
python examples/test_manual.py

# Legacy tests (updated to use new structure)
python test_invoices.py
python test_unit_invoices.py
```

## 🔄 Recent Refactoring (Individual Tool Files)

The tools layer has been refactored to split each MCP tool into its own dedicated file:

### Benefits of Individual Tool Files:
- **🔧 Modularity**: Each tool is self-contained and independently maintainable
- **🧪 Testability**: Individual unit tests for each tool with focused coverage
- **📚 Maintainability**: Smaller, focused files are easier to understand and modify
- **🔄 Scalability**: Easy to add new tools following the established pattern
- **👥 Team Collaboration**: Multiple developers can work on different tools simultaneously

### Tool File Pattern:
```python
# Example: get_invoices_by_date_tool.py

# Global service instance (lazy-loaded)
_invoice_service: InvoiceService = None

def get_invoice_service() -> InvoiceService:
    """Get or create service instance."""
    # Singleton pattern implementation

def register_get_invoices_by_date_tool(server: FastMCP) -> None:
    """Register the tool with the server."""

    @server.tool(name="get_invoices_by_date", ...)
    async def get_invoices_by_date(...):
        """Tool implementation that delegates to service."""
        service = get_invoice_service()
        return await service.method(...)

def get_invoice_service_for_testing() -> InvoiceService:
    """Direct service access for testing."""
    return get_invoice_service()
```

### Testing Pattern:
```python
# tests/unit/tools/test_get_invoices_by_date_tool.py
from src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool import (
    get_invoice_service_for_testing
)

# Test the service directly
service = get_invoice_service_for_testing()
result = await service.get_invoices_by_date_range(...)
```

This architecture ensures maintainable, testable, and scalable code while preserving all existing functionality.
