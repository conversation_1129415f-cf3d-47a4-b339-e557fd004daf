#!/usr/bin/env python3
"""Unit tests for the get_invoices_by_date MCP tool.

This demonstrates the proper way to test FastMCP tools by calling the
implementation function directly rather than the tool wrapper.
"""

import asyncio
from datetime import datetime
from dotenv import load_dotenv

try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False

# Load environment variables
load_dotenv()

# Import the service for testing (refactored individual tool approach)
from src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool import get_invoice_service_for_testing


class TestGetInvoicesByDate:
    """Unit tests for get_invoices_by_date functionality."""

    async def test_get_invoices_today(self):
        """Test getting invoices for today."""
        today = datetime.now().strftime("%Y-%m-%d")

        service = get_invoice_service_for_testing()
        result = await service.get_invoices_by_date_range(
            from_date=today,
            to_date=None,
            page_size=5
        )
        
        # Verify response structure
        assert isinstance(result, dict)
        assert 'data' in result
        assert 'summary' in result
        assert 'total' in result
        assert 'pageSize' in result
        
        # Verify summary structure
        summary = result['summary']
        assert 'total_invoices' in summary
        assert 'total_sum' in summary
        assert 'total_payment_sum' in summary
        assert 'date_range' in summary
        
        # Verify date range
        date_range = summary['date_range']
        assert date_range['from'] == f"{today}T00:00:00"
        assert date_range['to'] == f"{today}T23:59:59"
        
        print(f"✅ Test passed: Retrieved {summary['total_invoices']} invoices")
        print(f"💰 Total amount: {summary['total_sum']:,.0f}")
    
    async def test_get_invoices_date_range(self):
        """Test getting invoices for a specific date range."""
        from_date = "2025-07-19"
        to_date = "2025-07-20"

        service = get_invoice_service_for_testing()
        result = await service.get_invoices_by_date_range(
            from_date=from_date,
            to_date=to_date,
            page_size=10
        )
        
        # Verify response structure
        assert isinstance(result, dict)
        assert 'summary' in result
        
        # Verify date range formatting
        summary = result['summary']
        date_range = summary['date_range']
        assert date_range['from'] == f"{from_date}T00:00:00"
        assert date_range['to'] == f"{to_date}T23:59:59"
        
        print(f"✅ Test passed: Date range {from_date} to {to_date}")
        print(f"📊 Retrieved {summary['total_invoices']} invoices")
    
    async def test_get_invoices_with_iso_dates(self):
        """Test getting invoices with ISO formatted dates."""
        from_date = "2025-07-20T10:00:00"
        to_date = "2025-07-20T15:00:00"

        service = get_invoice_service_for_testing()
        result = await service.get_invoices_by_date_range(
            from_date=from_date,
            to_date=to_date,
            page_size=5
        )
        
        # Verify that ISO dates are preserved
        summary = result['summary']
        date_range = summary['date_range']
        assert date_range['from'] == from_date
        assert date_range['to'] == to_date
        
        print(f"✅ Test passed: ISO date range preserved")


def run_simple_test():
    """Run a simple test without pytest."""
    async def simple_test():
        today = datetime.now().strftime("%Y-%m-%d")
        print(f"🧪 Simple test: Getting invoices for {today}")
        
        try:
            service = get_invoice_service_for_testing()
            result = await service.get_invoices_by_date_range(
                from_date=today,
                page_size=3
            )
            
            summary = result['summary']
            print(f"✅ Success: {summary['total_invoices']} invoices, total: {summary['total_sum']:,.0f}")
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    return asyncio.run(simple_test())


if __name__ == "__main__":
    # Run simple test if called directly
    success = run_simple_test()
    exit(0 if success else 1)
