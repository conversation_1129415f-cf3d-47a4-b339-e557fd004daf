# Testing Guide for KiotViet MCP Server

## Problem Solved

The original test file `test_invoices.py` was failing with the error:
```
❌ Error: 'FunctionTool' object is not callable
```

This occurred because FastMCP's `@server.tool` decorator wraps functions in a `FunctionTool` object that cannot be called directly as a regular async function.

## Solution

We implemented a clean separation between the tool wrapper and the implementation:

1. **Implementation Function**: `_get_invoices_by_date_impl()` - Contains the actual business logic
2. **Tool Wrapper**: `get_invoices_by_date()` - FastMCP tool that delegates to the implementation

This pattern allows:
- ✅ Direct testing of the implementation function
- ✅ FastMCP tool functionality remains intact
- ✅ Clean separation of concerns
- ✅ Easy unit testing

## Test Files

### `test_invoices.py`
Main integration test that demonstrates the working solution:
```bash
python test_invoices.py
```

### `test_unit_invoices.py`
Unit test examples (works with or without pytest):
```bash
python test_unit_invoices.py
```

## Testing Pattern

When testing FastMCP tools, use this pattern:

```python
# ❌ DON'T: Try to call the tool function directly
from src.albatross_kiotviet_mcp.server import get_invoices_by_date
result = await get_invoices_by_date(...)  # This fails!

# ✅ DO: Call the implementation function
from src.albatross_kiotviet_mcp.server import _get_invoices_by_date_impl
result = await _get_invoices_by_date_impl(...)  # This works!
```

## Server Architecture

```
@server.tool decorator
        ↓
get_invoices_by_date() ──→ _get_invoices_by_date_impl()
    (FastMCP wrapper)           (Implementation)
        ↓                           ↑
    MCP Protocol              Direct testing
```

## Running Tests

1. **Integration Test**:
   ```bash
   python test_invoices.py
   ```

2. **Unit Tests**:
   ```bash
   python test_unit_invoices.py
   ```

3. **Server Test**:
   ```bash
   python -m src.albatross_kiotviet_mcp.server
   ```

## Expected Output

All tests should show:
- ✅ Successful API connection
- 📊 Invoice data retrieval
- 💰 Calculated totals
- 📅 Proper date range handling

Example:
```
✅ Function completed successfully!
📊 Response keys: ['total', 'pageSize', 'data', 'timestamp', 'summary']

💰 SUMMARY:
   📈 Total invoices: 10
   💵 Total amount: 4,005,487
   💳 Total payment: 2,892,152
   📅 Date range: 2025-07-20T00:00:00 to 2025-07-20T23:59:59
```

## Best Practices

1. **Always test implementation functions directly** for unit tests
2. **Use the tool wrapper only for integration tests** with MCP clients
3. **Separate business logic from MCP tool decorators** for better testability
4. **Include both success and error scenarios** in your tests
5. **Verify response structure and data types** in assertions

This pattern ensures your FastMCP tools are both functional and testable!
