#!/usr/bin/env python3
"""Test the new get_invoices_by_date MCP tool.

This test uses the new structured codebase to call the invoice service directly.
The new architecture separates business logic (in core/) from MCP tools (in tools/),
making testing much cleaner and more maintainable.
"""

import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the service for testing (refactored individual tool approach)
from src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool import get_invoice_service_for_testing

async def test_get_invoices():
    """Test the get_invoices_by_date function directly."""
    print("🧪 Testing get_invoices_by_date function...")
    print("📋 This will fetch invoices and calculate totals")
    print("-" * 60)

    try:
        # Test with today's date
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")

        print(f"📅 Testing with date: {today}")

        # Call the service directly using the new structured approach
        service = get_invoice_service_for_testing()
        result = await service.get_invoices_by_date_range(
            from_date=today,
            to_date=None,  # Will default to same day
            include_payment=True,
            include_invoice_delivery=True,
            current_item=0,
            page_size=10
        )
        
        print("✅ Function completed successfully!")
        print(f"📊 Response keys: {list(result.keys())}")
        
        if 'summary' in result:
            summary = result['summary']
            print("\n💰 SUMMARY:")
            print(f"   📈 Total invoices: {summary['total_invoices']}")
            print(f"   💵 Total amount: {summary['total_sum']:,.0f}")
            print(f"   💳 Total payment: {summary['total_payment_sum']:,.0f}")
            print(f"   📅 Date range: {summary['date_range']['from']} to {summary['date_range']['to']}")
        
        if 'data' in result and result['data']:
            print(f"\n📋 First few invoices:")
            for i, invoice in enumerate(result['data'][:3]):
                invoice_code = invoice.get('code', 'N/A')
                total = invoice.get('total', 0)
                total_payment = invoice.get('totalPayment', 0)
                print(f"   {i+1}. {invoice_code}: total={total:,.0f}, payment={total_payment:,.0f}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Make sure you have:")
        print("   - Valid KiotViet API credentials in .env file")
        print("   - Internet connection")
        print("   - Correct retailer name")
        print("   - Some invoices exist for the test date")

if __name__ == "__main__":
    asyncio.run(test_get_invoices())