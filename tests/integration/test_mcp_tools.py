"""Integration tests for MCP tools.

These tests verify that the MCP tools work correctly by calling the
underlying services directly (bypassing the FastMCP wrapper).
"""

from datetime import datetime

try:
    import pytest
    PYTEST_AVAILABLE = True
except ImportError:
    PYTEST_AVAILABLE = False

from src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool import get_invoice_service_for_testing
from src.albatross_kiotviet_mcp.tools.get_categories_tool import get_category_service_for_testing


class TestInvoiceToolsIntegration:
    """Integration tests for invoice MCP tools."""
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_today(self):
        """Test getting invoices for today (requires valid API credentials)."""
        service = get_invoice_service_for_testing()
        today = datetime.now().strftime("%Y-%m-%d")
        
        try:
            result = await service.get_invoices_by_date_range(
                from_date=today,
                page_size=5
            )
            
            # Verify response structure
            assert isinstance(result, dict)
            assert 'data' in result
            assert 'summary' in result
            
            # Verify summary structure
            summary = result['summary']
            assert 'total_invoices' in summary
            assert 'total_sum' in summary
            assert 'total_payment_sum' in summary
            assert 'date_range' in summary
            
            # Verify date range
            date_range = summary['date_range']
            assert date_range['from'] == f"{today}T00:00:00"
            assert date_range['to'] == f"{today}T23:59:59"
            
            print(f"✅ Integration test passed: Retrieved {summary['total_invoices']} invoices")
            print(f"💰 Total amount: {summary['total_sum']:,.0f}")
            
        except Exception as e:
            # If API credentials are not available, skip the test
            if "Failed to retrieve invoices" in str(e):
                pytest.skip(f"API credentials not available or API error: {e}")
            else:
                raise
    
    @pytest.mark.asyncio
    async def test_invoice_statistics_calculation(self):
        """Test invoice statistics calculation (requires valid API credentials)."""
        service = get_invoice_service_for_testing()
        today = datetime.now().strftime("%Y-%m-%d")
        
        try:
            # Get invoice data
            result = await service.get_invoices_by_date_range(
                from_date=today,
                page_size=10
            )
            
            # Calculate statistics
            invoices = result.get('data', [])
            if invoices:  # Only test if we have data
                stats = service.calculate_invoice_statistics(invoices)
                
                # Verify statistics structure
                assert 'count' in stats
                assert 'total_amount' in stats
                assert 'total_payment' in stats
                assert 'average_amount' in stats
                assert 'paid_invoices' in stats
                assert 'unpaid_invoices' in stats
                
                # Verify calculations make sense
                assert stats['count'] == len(invoices)
                assert stats['paid_invoices'] + stats['unpaid_invoices'] == stats['count']
                
                if stats['count'] > 0:
                    assert stats['average_amount'] == stats['total_amount'] / stats['count']
                
                print(f"✅ Statistics test passed: {stats['count']} invoices analyzed")
                print(f"📊 Paid: {stats['paid_invoices']}, Unpaid: {stats['unpaid_invoices']}")
            else:
                print("ℹ️ No invoices found for today, skipping statistics test")
                
        except Exception as e:
            if "Failed to retrieve invoices" in str(e):
                pytest.skip(f"API credentials not available or API error: {e}")
            else:
                raise


class TestCategoryToolsIntegration:
    """Integration tests for category MCP tools."""
    
    @pytest.mark.asyncio
    async def test_get_categories_basic(self):
        """Test getting categories (requires valid API credentials)."""
        service = get_category_service_for_testing()
        
        try:
            result = await service.get_categories(page_size=10)
            
            # Verify response structure
            assert isinstance(result, dict)
            assert 'data' in result
            assert 'metadata' in result
            
            # Verify metadata structure
            metadata = result['metadata']
            assert 'total_categories' in metadata
            assert 'has_subcategories' in metadata
            assert 'category_names' in metadata
            
            categories = result['data']
            print(f"✅ Categories test passed: Retrieved {len(categories)} categories")
            
            if categories:
                print(f"📂 Sample categories: {metadata['category_names'][:3]}")
                
        except Exception as e:
            if "Failed to retrieve categories" in str(e):
                pytest.skip(f"API credentials not available or API error: {e}")
            else:
                raise
    
    @pytest.mark.asyncio
    async def test_category_tree_building(self):
        """Test building category tree (requires valid API credentials)."""
        service = get_category_service_for_testing()
        
        try:
            # Get categories first
            result = await service.get_categories(page_size=50)
            categories = result.get('data', [])
            
            if categories:
                # Build tree
                tree = service.build_category_tree(categories)
                
                # Verify tree structure
                assert 'root_categories' in tree
                assert 'total_categories' in tree
                assert 'tree_depth' in tree
                
                assert tree['total_categories'] == len(categories)
                assert tree['tree_depth'] >= 1
                
                print(f"✅ Tree building test passed: {tree['total_categories']} categories")
                print(f"🌳 Tree depth: {tree['tree_depth']}")
                print(f"🌿 Root categories: {len(tree['root_categories'])}")
            else:
                print("ℹ️ No categories found, skipping tree building test")
                
        except Exception as e:
            if "Failed to retrieve categories" in str(e):
                pytest.skip(f"API credentials not available or API error: {e}")
            else:
                raise
    
    @pytest.mark.asyncio
    async def test_category_search(self):
        """Test category search functionality (requires valid API credentials)."""
        service = get_category_service_for_testing()
        
        try:
            # Get categories first
            result = await service.get_categories(page_size=50)
            categories = result.get('data', [])
            
            if categories:
                # Test search with first category name
                first_category_name = categories[0].get('categoryName', '')
                if first_category_name:
                    # Search for part of the name
                    search_term = first_category_name[:3].lower()
                    matches = service.search_categories(categories, search_term)
                    
                    # Should find at least the first category
                    assert len(matches) >= 1
                    
                    print(f"✅ Search test passed: Found {len(matches)} matches for '{search_term}'")
                    print(f"🔍 Sample matches: {[m['categoryName'] for m in matches[:3]]}")
                else:
                    print("ℹ️ No category names available for search test")
            else:
                print("ℹ️ No categories found, skipping search test")
                
        except Exception as e:
            if "Failed to retrieve categories" in str(e):
                pytest.skip(f"API credentials not available or API error: {e}")
            else:
                raise


# Simple test runner for manual execution
async def run_simple_integration_tests():
    """Run simple integration tests without pytest."""
    print("🧪 Running simple integration tests...")
    
    # Test invoices
    try:
        service = get_invoice_service_for_testing()
        today = datetime.now().strftime("%Y-%m-%d")
        
        result = await service.get_invoices_by_date_range(
            from_date=today,
            page_size=3
        )
        
        summary = result['summary']
        print(f"✅ Invoice test: {summary['total_invoices']} invoices, total: {summary['total_sum']:,.0f}")
        
    except Exception as e:
        print(f"❌ Invoice test failed: {e}")
    
    # Test categories
    try:
        service = get_category_service_for_testing()
        
        result = await service.get_categories(page_size=5)
        categories = result['data']
        
        print(f"✅ Category test: {len(categories)} categories retrieved")
        
    except Exception as e:
        print(f"❌ Category test failed: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_simple_integration_tests())
