"""Pytest configuration and shared fixtures for KiotViet MCP tests."""

import pytest
import asyncio
from dotenv import load_dotenv

# Load environment variables for testing
load_dotenv()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_invoice_data():
    """Sample invoice data for testing."""
    return [
        {
            'id': 1,
            'code': 'HD001',
            'total': 100000,
            'totalPayment': 100000,
            'purchaseDate': '2025-07-20T10:00:00'
        },
        {
            'id': 2,
            'code': 'HD002',
            'total': 200000,
            'totalPayment': 150000,
            'purchaseDate': '2025-07-20T11:00:00'
        },
        {
            'id': 3,
            'code': 'HD003',
            'total': 300000,
            'totalPayment': 0,
            'purchaseDate': '2025-07-20T12:00:00'
        }
    ]


@pytest.fixture
def sample_category_data():
    """Sample category data for testing."""
    return [
        {
            'categoryId': 1,
            'categoryName': 'Electronics',
            'parentId': None,
            'hasChild': True
        },
        {
            'categoryId': 2,
            'categoryName': 'Smartphones',
            'parentId': 1,
            'hasChild': False
        },
        {
            'categoryId': 3,
            'categoryName': 'Clothing',
            'parentId': None,
            'hasChild': True
        },
        {
            'categoryId': 4,
            'categoryName': 'T-Shirts',
            'parentId': 3,
            'hasChild': False
        }
    ]
