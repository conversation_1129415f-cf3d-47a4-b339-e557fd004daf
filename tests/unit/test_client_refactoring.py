"""Tests for the refactored KiotVietAPIClient."""

import pytest
from unittest.mock import AsyncMock, MagicMock
import httpx

from src.albatross_kiotviet_mcp.client import (
    KiotVietAPIClient,
    KiotVietAPIError,
    AuthenticationError,
    ValidationError,
    RateLimitError,
    APIConnectionError,
    OrderDirection,
    APIConstants
)
from src.albatross_kiotviet_mcp.config import KiotVietConfig


class TestKiotVietAPIClientRefactoring:
    """Test cases for the refactored KiotVietAPIClient."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration."""
        config = MagicMock(spec=KiotVietConfig)
        config.api_base_url = "https://api.test.com"
        config.request_timeout = 30
        config.retailer = "test_retailer"
        config.max_retries = 3
        return config
    
    @pytest.fixture
    def mock_token_manager(self):
        """Create a mock token manager."""
        token_manager = AsyncMock()
        token_manager.get_access_token.return_value = "test_token"
        return token_manager
    
    @pytest.fixture
    def client(self, mock_config, mock_token_manager):
        """Create a client instance with mocked dependencies."""
        client = KiotVietAPIClient(mock_config)
        client.token_manager = mock_token_manager
        return client
    
    def test_constants_defined(self):
        """Test that API constants are properly defined."""
        assert APIConstants.MAX_PAGE_SIZE == 100
        assert APIConstants.MIN_PAGE_SIZE == 1
        assert APIConstants.DEFAULT_PAGE_SIZE == 50
        assert APIConstants.DEFAULT_CURRENT_ITEM == 0
        assert APIConstants.MAX_RETRIES == 3
        assert APIConstants.RETRY_BASE_DELAY == 2
    
    def test_order_direction_enum(self):
        """Test that OrderDirection enum is properly defined."""
        assert OrderDirection.ASC == "Asc"
        assert OrderDirection.DESC == "Desc"
    
    def test_custom_exceptions_defined(self):
        """Test that custom exceptions are properly defined."""
        # Test exception hierarchy
        assert issubclass(AuthenticationError, KiotVietAPIError)
        assert issubclass(ValidationError, KiotVietAPIError)
        assert issubclass(RateLimitError, KiotVietAPIError)
        assert issubclass(APIConnectionError, KiotVietAPIError)
        
        # Test exception instantiation
        auth_error = AuthenticationError("Auth failed")
        assert str(auth_error) == "Auth failed"
    
    def test_validate_pagination_params_valid(self, client):
        """Test pagination parameter validation with valid values."""
        # Should not raise any exception
        client._validate_pagination_params(50, 0)
        client._validate_pagination_params(1, 100)
        client._validate_pagination_params(100, 0)
    
    def test_validate_pagination_params_invalid_page_size(self, client):
        """Test pagination parameter validation with invalid page size."""
        with pytest.raises(ValidationError) as exc_info:
            client._validate_pagination_params(0, 0)
        assert "page_size must be an integer between 1 and 100" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_pagination_params(101, 0)
        assert "page_size must be an integer between 1 and 100" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_pagination_params("invalid", 0)
        assert "page_size must be an integer between 1 and 100" in str(exc_info.value)
    
    def test_validate_pagination_params_invalid_current_item(self, client):
        """Test pagination parameter validation with invalid current_item."""
        with pytest.raises(ValidationError) as exc_info:
            client._validate_pagination_params(50, -1)
        assert "current_item must be a non-negative integer" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_pagination_params(50, "invalid")
        assert "current_item must be a non-negative integer" in str(exc_info.value)
    
    def test_validate_order_direction_valid(self, client):
        """Test order direction validation with valid values."""
        # Should not raise any exception
        client._validate_order_direction("Asc")
        client._validate_order_direction("Desc")
        client._validate_order_direction(OrderDirection.ASC)
        client._validate_order_direction(OrderDirection.DESC)
    
    def test_validate_order_direction_invalid(self, client):
        """Test order direction validation with invalid values."""
        with pytest.raises(ValidationError) as exc_info:
            client._validate_order_direction("Invalid")
        assert "order_direction must be 'Asc' or 'Desc'" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_order_direction(123)
        assert "order_direction must be 'Asc' or 'Desc'" in str(exc_info.value)
    
    def test_validate_date_range_valid(self, client):
        """Test date range validation with valid values."""
        # Should not raise any exception
        client._validate_date_range("2025-07-20T00:00:00", "2025-07-20T23:59:59")
        client._validate_date_range("2025-01-01T00:00:00", "2025-12-31T23:59:59")
    
    def test_validate_date_range_invalid(self, client):
        """Test date range validation with invalid values."""
        with pytest.raises(ValidationError) as exc_info:
            client._validate_date_range("", "2025-07-20T23:59:59")
        assert "from_purchase_date must be a non-empty string" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_date_range("2025-07-20T00:00:00", "")
        assert "to_purchase_date must be a non-empty string" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            client._validate_date_range("short", "2025-07-20T23:59:59")
        assert "Date strings must be in ISO format" in str(exc_info.value)
    
    def test_build_request_data(self, client):
        """Test request data building."""
        data = client._build_request_data(
            pageSize=50,
            currentItem=0,
            orderDirection="Asc",
            includePayment=True,
            emptyValue=None
        )
        
        expected = {
            "pageSize": 50,
            "currentItem": 0,
            "orderDirection": "Asc",
            "includePayment": True
        }
        
        assert data == expected
        assert "emptyValue" not in data
    
    @pytest.mark.asyncio
    async def test_get_headers_success(self, client, mock_token_manager):
        """Test successful header generation."""
        mock_token_manager.get_access_token.return_value = "test_access_token"
        
        headers = await client._get_headers()
        
        expected_headers = {
            "Retailer": "test_retailer",
            "Authorization": "Bearer test_access_token",
            "Content-Type": "application/json"
        }
        
        assert headers == expected_headers
        mock_token_manager.get_access_token.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_headers_authentication_error(self, client, mock_token_manager):
        """Test header generation with authentication error."""
        mock_token_manager.get_access_token.side_effect = Exception("Token error")
        
        with pytest.raises(AuthenticationError) as exc_info:
            await client._get_headers()
        
        assert "Authentication failed: Token error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_context_manager(self, client):
        """Test async context manager functionality."""
        async with client as ctx_client:
            assert ctx_client is client
            assert client._client is not None
            assert isinstance(client._client, httpx.AsyncClient)
        
        # Client should be closed after context exit
        # Note: We can't easily test this without mocking httpx.AsyncClient
    
    def test_wait_for_retry_calculation(self, client):
        """Test retry delay calculation."""
        # Test exponential backoff calculation
        import asyncio
        
        async def test_delays():
            # We can't easily test the actual sleep, but we can test the delay calculation
            # by checking the formula: 2^attempt
            for attempt in range(3):
                expected_delay = APIConstants.RETRY_BASE_DELAY ** attempt
                # The actual delay calculation is: 2^attempt
                assert expected_delay == 2 ** attempt
        
        # Run the async test
        asyncio.run(test_delays())
