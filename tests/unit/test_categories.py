"""Unit tests for category business logic."""

import pytest
from unittest.mock import AsyncMock

from src.albatross_kiotviet_mcp.core.categories import CategoryService


class TestCategoryService:
    """Test cases for CategoryService."""
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock KiotViet API client."""
        client = AsyncMock()
        client.__aenter__ = AsyncMock(return_value=client)
        client.__aexit__ = AsyncMock(return_value=None)
        return client
    
    @pytest.fixture
    def category_service(self, mock_client):
        """Create a CategoryService instance with mock client."""
        return CategoryService(mock_client)
    
    def test_validate_category_params_valid(self, category_service):
        """Test parameter validation with valid parameters."""
        # Should not raise any exception
        category_service._validate_category_params(50, 0, "Asc")
        category_service._validate_category_params(100, 10, "Desc")
    
    def test_validate_category_params_invalid_page_size(self, category_service):
        """Test parameter validation with invalid page size."""
        with pytest.raises(ValueError) as exc_info:
            category_service._validate_category_params(0, 0, "Asc")
        assert "page_size must be between 1 and 100" in str(exc_info.value)
        
        with pytest.raises(ValueError) as exc_info:
            category_service._validate_category_params(101, 0, "Asc")
        assert "page_size must be between 1 and 100" in str(exc_info.value)
    
    def test_validate_category_params_invalid_current_item(self, category_service):
        """Test parameter validation with invalid current_item."""
        with pytest.raises(ValueError) as exc_info:
            category_service._validate_category_params(50, -1, "Asc")
        assert "current_item must be non-negative" in str(exc_info.value)
    
    def test_validate_category_params_invalid_order_direction(self, category_service):
        """Test parameter validation with invalid order direction."""
        with pytest.raises(ValueError) as exc_info:
            category_service._validate_category_params(50, 0, "Invalid")
        assert "order_direction must be 'Asc' or 'Desc'" in str(exc_info.value)
    
    def test_enhance_category_result(self, category_service, sample_category_data):
        """Test category result enhancement with metadata."""
        result = {'data': sample_category_data}
        
        enhanced = category_service._enhance_category_result(result)
        
        assert 'metadata' in enhanced
        metadata = enhanced['metadata']
        
        assert metadata['total_categories'] == 4
        assert metadata['has_subcategories'] == True  # Electronics and Clothing have children
        assert len(metadata['category_names']) == 4
        assert 'Electronics' in metadata['category_names']
        assert 'Clothing' in metadata['category_names']
    
    def test_build_category_tree(self, category_service, sample_category_data):
        """Test building hierarchical category tree."""
        tree = category_service.build_category_tree(sample_category_data)
        
        assert 'root_categories' in tree
        assert 'total_categories' in tree
        assert 'tree_depth' in tree
        
        assert tree['total_categories'] == 4
        assert tree['tree_depth'] == 2  # Root -> Child
        
        root_categories = tree['root_categories']
        assert len(root_categories) == 2  # Electronics and Clothing
        
        # Check Electronics has children
        electronics = next(cat for cat in root_categories if cat['categoryName'] == 'Electronics')
        assert 'children' in electronics
        assert len(electronics['children']) == 1
        assert electronics['children'][0]['categoryName'] == 'Smartphones'
    
    def test_calculate_tree_depth_empty(self, category_service):
        """Test tree depth calculation with empty categories."""
        depth = category_service._calculate_tree_depth([])
        assert depth == 0
    
    def test_calculate_tree_depth_single_level(self, category_service):
        """Test tree depth calculation with single level."""
        categories = [{'categoryName': 'Root', 'children': []}]
        depth = category_service._calculate_tree_depth(categories)
        assert depth == 1
    
    def test_search_categories(self, category_service, sample_category_data):
        """Test category search functionality."""
        # Search for "Electronics"
        results = category_service.search_categories(sample_category_data, "electronics")
        assert len(results) == 1
        assert results[0]['categoryName'] == 'Electronics'
        
        # Search for "phone" (should match "Smartphones")
        results = category_service.search_categories(sample_category_data, "phone")
        assert len(results) == 1
        assert results[0]['categoryName'] == 'Smartphones'
        
        # Search for non-existent term
        results = category_service.search_categories(sample_category_data, "nonexistent")
        assert len(results) == 0
    
    @pytest.mark.asyncio
    async def test_get_categories_success(self, category_service, mock_client, sample_category_data):
        """Test successful category retrieval."""
        # Setup mock response
        mock_response = {
            'data': sample_category_data,
            'total': 4,
            'pageSize': 50
        }
        mock_client.get_categories.return_value = mock_response
        
        # Call the method
        result = await category_service.get_categories(page_size=50)
        
        # Verify the result
        assert 'data' in result
        assert 'metadata' in result
        assert result['metadata']['total_categories'] == 4
        
        # Verify client was called correctly
        mock_client.get_categories.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction="Asc",
            hierarchical_data=False
        )
    
    @pytest.mark.asyncio
    async def test_get_categories_api_error(self, category_service, mock_client):
        """Test category retrieval with API error."""
        # Setup mock to raise exception
        mock_client.get_categories.side_effect = Exception("API Error")
        
        # Verify exception is raised
        with pytest.raises(Exception) as exc_info:
            await category_service.get_categories()
        
        assert "Failed to retrieve categories" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_all_categories_single_page(self, category_service, mock_client, sample_category_data):
        """Test getting all categories with single page."""
        # Setup mock response
        mock_response = {
            'data': sample_category_data,
            'total': 4,
            'pageSize': 100
        }
        mock_client.get_categories.return_value = mock_response
        
        # Call the method
        result = await category_service.get_all_categories()
        
        # Verify the result
        assert len(result) == 4
        assert result == sample_category_data
        
        # Verify client was called once (all data in first page)
        mock_client.get_categories.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_categories_multiple_pages(self, category_service, mock_client, sample_category_data):
        """Test getting all categories with multiple pages."""
        # Setup mock responses for pagination
        page1_data = sample_category_data[:2]
        page2_data = sample_category_data[2:]
        
        mock_client.get_categories.side_effect = [
            {'data': page1_data, 'total': 4, 'pageSize': 2},
            {'data': page2_data, 'total': 4, 'pageSize': 2}
        ]
        
        # Call the method
        result = await category_service.get_all_categories()
        
        # Verify the result
        assert len(result) == 4
        assert result == sample_category_data
        
        # Verify client was called twice
        assert mock_client.get_categories.call_count == 2
