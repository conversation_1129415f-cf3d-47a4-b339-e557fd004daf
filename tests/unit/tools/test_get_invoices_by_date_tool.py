"""Unit tests for get_invoices_by_date_tool."""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool import (
    get_invoice_service_for_testing,
    get_invoice_service
)


class TestGetInvoicesByDateTool:
    """Test cases for get_invoices_by_date tool."""
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_success(self):
        """Test successful invoice retrieval by date."""
        # Mock the service
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock response
            mock_response = {
                'data': [
                    {'id': 1, 'code': 'HD001', 'total': 100000, 'totalPayment': 100000},
                    {'id': 2, 'code': 'HD002', 'total': 200000, 'totalPayment': 150000}
                ],
                'summary': {
                    'total_invoices': 2,
                    'total_sum': 300000,
                    'total_payment_sum': 250000,
                    'date_range': {
                        'from': '2025-07-20T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            mock_service.get_invoices_by_date_range.return_value = mock_response
            
            # Test the service function directly
            service = get_invoice_service_for_testing()
            result = await service.get_invoices_by_date_range(
                from_date="2025-07-20",
                to_date=None,
                page_size=10
            )
            
            # Verify the result
            assert 'data' in result
            assert 'summary' in result
            assert result['summary']['total_invoices'] == 2
            assert result['summary']['total_sum'] == 300000
            
            # Verify service was called correctly
            mock_service.get_invoices_by_date_range.assert_called_once_with(
                from_date="2025-07-20",
                to_date=None,
                include_payment=True,
                include_invoice_delivery=True,
                current_item=0,
                page_size=10
            )
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_with_date_range(self):
        """Test invoice retrieval with specific date range."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [],
                'summary': {
                    'total_invoices': 0,
                    'total_sum': 0,
                    'total_payment_sum': 0,
                    'date_range': {
                        'from': '2025-07-19T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            mock_service.get_invoices_by_date_range.return_value = mock_response
            
            service = get_invoice_service_for_testing()
            result = await service.get_invoices_by_date_range(
                from_date="2025-07-19",
                to_date="2025-07-20",
                page_size=50
            )
            
            assert result['summary']['date_range']['from'] == '2025-07-19T00:00:00'
            assert result['summary']['date_range']['to'] == '2025-07-20T23:59:59'
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_api_error(self):
        """Test invoice retrieval with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_invoices_by_date_range.side_effect = Exception("API Error")
            
            service = get_invoice_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_invoices_by_date_range("2025-07-20")
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_with_custom_parameters(self):
        """Test invoice retrieval with custom parameters."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [{'id': 1, 'code': 'HD001', 'total': 100000}],
                'summary': {
                    'total_invoices': 1,
                    'total_sum': 100000,
                    'total_payment_sum': 100000,
                    'date_range': {
                        'from': '2025-07-20T10:00:00',
                        'to': '2025-07-20T15:00:00'
                    }
                }
            }
            mock_service.get_invoices_by_date_range.return_value = mock_response
            
            service = get_invoice_service_for_testing()
            result = await service.get_invoices_by_date_range(
                from_date="2025-07-20T10:00:00",
                to_date="2025-07-20T15:00:00",
                include_payment=False,
                include_invoice_delivery=False,
                current_item=10,
                page_size=25
            )
            
            # Verify service was called with custom parameters
            mock_service.get_invoices_by_date_range.assert_called_once_with(
                from_date="2025-07-20T10:00:00",
                to_date="2025-07-20T15:00:00",
                include_payment=False,
                include_invoice_delivery=False,
                current_item=10,
                page_size=25
            )
    
    def test_get_invoice_service_singleton(self):
        """Test that get_invoice_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.InvoiceService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool as tool_module
                    tool_module._invoice_service = None
                    
                    # Get service twice
                    service1 = get_invoice_service()
                    service2 = get_invoice_service()
                    
                    # Should be the same instance
                    assert service1 is service2
                    
                    # Config and client should only be called once
                    mock_config.assert_called_once()
                    mock_client.assert_called_once()
                    mock_service.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_empty_result(self):
        """Test invoice retrieval with empty result."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoices_by_date_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [],
                'summary': {
                    'total_invoices': 0,
                    'total_sum': 0,
                    'total_payment_sum': 0,
                    'date_range': {
                        'from': '2025-07-20T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            mock_service.get_invoices_by_date_range.return_value = mock_response
            
            service = get_invoice_service_for_testing()
            result = await service.get_invoices_by_date_range("2025-07-20")
            
            assert result['data'] == []
            assert result['summary']['total_invoices'] == 0
            assert result['summary']['total_sum'] == 0
