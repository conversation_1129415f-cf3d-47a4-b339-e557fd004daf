"""Unit tests for get_categories_tool."""

import pytest
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.get_categories_tool import (
    get_category_service_for_testing,
    get_category_service
)


class TestGetCategoriesTool:
    """Test cases for get_categories tool."""
    
    @pytest.mark.asyncio
    async def test_get_categories_success(self):
        """Test successful category retrieval."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock response
            mock_response = {
                'data': [
                    {'categoryId': 1, 'categoryName': 'Electronics', 'hasChild': True},
                    {'categoryId': 2, 'categoryName': 'Clothing', 'hasChild': False}
                ],
                'metadata': {
                    'total_categories': 2,
                    'has_subcategories': True,
                    'category_names': ['Electronics', 'Clothing']
                },
                'total': 2,
                'pageSize': 50
            }
            mock_service.get_categories.return_value = mock_response
            
            # Test the service function directly
            service = get_category_service_for_testing()
            result = await service.get_categories(
                page_size=50,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )
            
            # Verify the result
            assert 'data' in result
            assert 'metadata' in result
            assert len(result['data']) == 2
            assert result['metadata']['total_categories'] == 2
            assert result['metadata']['has_subcategories'] == True
            
            # Verify service was called correctly
            mock_service.get_categories.assert_called_once_with(
                page_size=50,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=False
            )
    
    @pytest.mark.asyncio
    async def test_get_categories_with_custom_parameters(self):
        """Test category retrieval with custom parameters."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [],
                'metadata': {'total_categories': 0, 'has_subcategories': False, 'category_names': []},
                'total': 0,
                'pageSize': 25
            }
            mock_service.get_categories.return_value = mock_response
            
            service = get_category_service_for_testing()
            result = await service.get_categories(
                page_size=25,
                current_item=10,
                order_direction="Desc",
                hierarchical_data=True
            )
            
            # Verify service was called with custom parameters
            mock_service.get_categories.assert_called_once_with(
                page_size=25,
                current_item=10,
                order_direction="Desc",
                hierarchical_data=True
            )
    
    @pytest.mark.asyncio
    async def test_get_categories_api_error(self):
        """Test category retrieval with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_categories.side_effect = Exception("API Error")
            
            service = get_category_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_categories()
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_categories_empty_result(self):
        """Test category retrieval with empty result."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [],
                'metadata': {
                    'total_categories': 0,
                    'has_subcategories': False,
                    'category_names': []
                },
                'total': 0,
                'pageSize': 50
            }
            mock_service.get_categories.return_value = mock_response
            
            service = get_category_service_for_testing()
            result = await service.get_categories()
            
            assert result['data'] == []
            assert result['metadata']['total_categories'] == 0
            assert result['metadata']['has_subcategories'] == False
    
    @pytest.mark.asyncio
    async def test_get_categories_hierarchical_data(self):
        """Test category retrieval with hierarchical data."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_response = {
                'data': [
                    {
                        'categoryId': 1,
                        'categoryName': 'Electronics',
                        'hasChild': True,
                        'children': [
                            {'categoryId': 2, 'categoryName': 'Smartphones', 'hasChild': False}
                        ]
                    }
                ],
                'metadata': {
                    'total_categories': 1,
                    'has_subcategories': True,
                    'category_names': ['Electronics']
                }
            }
            mock_service.get_categories.return_value = mock_response
            
            service = get_category_service_for_testing()
            result = await service.get_categories(hierarchical_data=True)
            
            # Verify hierarchical structure
            assert len(result['data']) == 1
            assert 'children' in result['data'][0]
            assert len(result['data'][0]['children']) == 1
            
            # Verify service was called with hierarchical_data=True
            mock_service.get_categories.assert_called_once_with(
                page_size=50,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=True
            )
    
    @pytest.mark.asyncio
    async def test_get_categories_validation_error(self):
        """Test category retrieval with validation error."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise validation error
            mock_service.get_categories.side_effect = ValueError("page_size must be between 1 and 100")
            
            service = get_category_service_for_testing()
            
            # Verify validation error is raised
            with pytest.raises(ValueError) as exc_info:
                await service.get_categories(page_size=150)
            
            assert "page_size must be between 1 and 100" in str(exc_info.value)
    
    def test_get_category_service_singleton(self):
        """Test that get_category_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.get_categories_tool.CategoryService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.get_categories_tool as tool_module
                    tool_module._category_service = None
                    
                    # Get service twice
                    service1 = get_category_service()
                    service2 = get_category_service()
                    
                    # Should be the same instance
                    assert service1 is service2
                    
                    # Config and client should only be called once
                    mock_config.assert_called_once()
                    mock_client.assert_called_once()
                    mock_service.assert_called_once()
