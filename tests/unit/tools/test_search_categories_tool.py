"""Unit tests for search_categories_tool."""

import pytest
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.search_categories_tool import (
    get_category_service_for_testing,
    get_category_service
)


class TestSearchCategoriesTool:
    """Test cases for search_categories tool."""
    
    @pytest.mark.asyncio
    async def test_search_categories_success(self):
        """Test successful category search."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock categories
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics', 'description': 'Electronic devices'},
                {'categoryId': 2, 'categoryName': 'Smartphones', 'description': 'Mobile phones'},
                {'categoryId': 3, 'categoryName': 'Clothing', 'description': 'Apparel and fashion'}
            ]
            
            # Setup mock search results
            mock_search_results = [
                {'categoryId': 1, 'categoryName': 'Electronics', 'description': 'Electronic devices'},
                {'categoryId': 2, 'categoryName': 'Smartphones', 'description': 'Mobile phones'}
            ]
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.search_categories.return_value = mock_search_results
            
            # Test the service functions directly
            service = get_category_service_for_testing()
            
            # Get all categories
            categories = await service.get_all_categories(
                hierarchical_data=False,
                max_pages=10
            )
            
            # Search categories
            matches = service.search_categories(categories, "phone")
            
            # Verify the results
            assert len(matches) == 2
            assert matches[0]['categoryName'] == 'Electronics'
            assert matches[1]['categoryName'] == 'Smartphones'
            
            # Verify service calls
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=10
            )
            mock_service.search_categories.assert_called_once_with(mock_categories, "phone")
    
    @pytest.mark.asyncio
    async def test_search_categories_first_page_only(self):
        """Test category search in first page only."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock response for get_categories
            mock_categories_response = {
                'data': [
                    {'categoryId': 1, 'categoryName': 'Electronics'},
                    {'categoryId': 2, 'categoryName': 'Clothing'}
                ]
            }
            
            mock_search_results = [
                {'categoryId': 1, 'categoryName': 'Electronics'}
            ]
            
            mock_service.get_categories.return_value = mock_categories_response
            mock_service.search_categories.return_value = mock_search_results
            
            service = get_category_service_for_testing()
            
            # Get first page categories
            result = await service.get_categories(page_size=100)
            categories = result.get('data', [])
            
            # Search categories
            matches = service.search_categories(categories, "electronics")
            
            # Verify the results
            assert len(matches) == 1
            assert matches[0]['categoryName'] == 'Electronics'
            
            # Verify service calls
            mock_service.get_categories.assert_called_once_with(page_size=100)
            mock_service.search_categories.assert_called_once_with(mock_categories_response['data'], "electronics")
    
    @pytest.mark.asyncio
    async def test_search_categories_no_matches(self):
        """Test category search with no matches."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'Clothing'}
            ]
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.search_categories.return_value = []
            
            service = get_category_service_for_testing()
            
            categories = await service.get_all_categories()
            matches = service.search_categories(categories, "nonexistent")
            
            # Verify no matches
            assert matches == []
    
    @pytest.mark.asyncio
    async def test_search_categories_empty_search_term(self):
        """Test category search with empty search term."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            service = get_category_service_for_testing()
            
            # Test with empty string
            with pytest.raises(ValueError) as exc_info:
                categories = await service.get_all_categories()
                service.search_categories(categories, "")
            
            # Note: The actual validation would happen in the MCP tool wrapper
            # For now, we test that the service method would be called
    
    @pytest.mark.asyncio
    async def test_search_categories_api_error(self):
        """Test category search with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_all_categories.side_effect = Exception("API Error")
            
            service = get_category_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_all_categories()
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_search_categories_case_insensitive(self):
        """Test category search is case insensitive."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'SMARTPHONES'}
            ]
            
            # Mock search should return both for case-insensitive search
            mock_search_results = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'SMARTPHONES'}
            ]
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.search_categories.return_value = mock_search_results
            
            service = get_category_service_for_testing()
            
            categories = await service.get_all_categories()
            matches = service.search_categories(categories, "ELECTRONICS")
            
            # Should find matches regardless of case
            assert len(matches) == 2
    
    @pytest.mark.asyncio
    async def test_search_categories_custom_max_pages(self):
        """Test category search with custom max_pages."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [{'categoryId': 1, 'categoryName': 'Test'}]
            mock_search_results = [{'categoryId': 1, 'categoryName': 'Test'}]
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.search_categories.return_value = mock_search_results
            
            service = get_category_service_for_testing()
            
            # Test with custom max_pages
            categories = await service.get_all_categories(max_pages=5)
            matches = service.search_categories(categories, "test")
            
            # Verify service was called with custom max_pages
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=5
            )
    
    @pytest.mark.asyncio
    async def test_search_categories_partial_match(self):
        """Test category search with partial matches."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'Electronic Accessories'},
                {'categoryId': 3, 'categoryName': 'Clothing'}
            ]
            
            # Mock partial matches
            mock_search_results = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'Electronic Accessories'}
            ]
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.search_categories.return_value = mock_search_results
            
            service = get_category_service_for_testing()
            
            categories = await service.get_all_categories()
            matches = service.search_categories(categories, "electronic")
            
            # Should find partial matches
            assert len(matches) == 2
            assert all("Electronic" in match['categoryName'] for match in matches)
    
    def test_get_category_service_singleton(self):
        """Test that get_category_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.search_categories_tool.CategoryService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.search_categories_tool as tool_module
                    tool_module._category_service = None
                    
                    # Get service twice
                    service1 = get_category_service()
                    service2 = get_category_service()
                    
                    # Should be the same instance
                    assert service1 is service2
