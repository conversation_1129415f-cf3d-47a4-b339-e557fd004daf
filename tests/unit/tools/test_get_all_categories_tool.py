"""Unit tests for get_all_categories_tool."""

import pytest
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.get_all_categories_tool import (
    get_category_service_for_testing,
    get_category_service
)


class TestGetAllCategoriesTool:
    """Test cases for get_all_categories tool."""
    
    @pytest.mark.asyncio
    async def test_get_all_categories_success(self):
        """Test successful retrieval of all categories."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock response
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics'},
                {'categoryId': 2, 'categoryName': 'Clothing'},
                {'categoryId': 3, 'categoryName': 'Books'},
                {'categoryId': 4, 'categoryName': 'Sports'}
            ]
            mock_service.get_all_categories.return_value = mock_categories
            
            # Test the service function directly
            service = get_category_service_for_testing()
            result = await service.get_all_categories(
                hierarchical_data=False,
                max_pages=10
            )
            
            # Verify the result
            assert len(result) == 4
            assert result[0]['categoryName'] == 'Electronics'
            assert result[3]['categoryName'] == 'Sports'
            
            # Verify service was called correctly
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=10
            )
    
    @pytest.mark.asyncio
    async def test_get_all_categories_with_hierarchical_data(self):
        """Test retrieval of all categories with hierarchical data."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [
                {
                    'categoryId': 1,
                    'categoryName': 'Electronics',
                    'children': [
                        {'categoryId': 2, 'categoryName': 'Smartphones'}
                    ]
                }
            ]
            mock_service.get_all_categories.return_value = mock_categories
            
            service = get_category_service_for_testing()
            result = await service.get_all_categories(hierarchical_data=True)
            
            # Verify hierarchical structure
            assert len(result) == 1
            assert 'children' in result[0]
            assert len(result[0]['children']) == 1
            
            # Verify service was called with hierarchical_data=True
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=True,
                max_pages=10
            )
    
    @pytest.mark.asyncio
    async def test_get_all_categories_custom_max_pages(self):
        """Test retrieval with custom max_pages parameter."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [{'categoryId': 1, 'categoryName': 'Test'}]
            mock_service.get_all_categories.return_value = mock_categories
            
            service = get_category_service_for_testing()
            result = await service.get_all_categories(max_pages=5)
            
            # Verify service was called with custom max_pages
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=5
            )
    
    @pytest.mark.asyncio
    async def test_get_all_categories_empty_result(self):
        """Test retrieval with empty result."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_service.get_all_categories.return_value = []
            
            service = get_category_service_for_testing()
            result = await service.get_all_categories()
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_get_all_categories_api_error(self):
        """Test retrieval with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_all_categories.side_effect = Exception("API Error")
            
            service = get_category_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_all_categories()
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_all_categories_pagination_limit(self):
        """Test retrieval with pagination limit reached."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Simulate reaching max_pages limit
            mock_categories = [{'categoryId': i, 'categoryName': f'Category {i}'} for i in range(1, 101)]
            mock_service.get_all_categories.return_value = mock_categories
            
            service = get_category_service_for_testing()
            result = await service.get_all_categories(max_pages=1)
            
            # Should still return the categories (service handles pagination internally)
            assert len(result) == 100
            
            # Verify service was called with max_pages=1
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=1
            )
    
    @pytest.mark.asyncio
    async def test_get_all_categories_large_dataset(self):
        """Test retrieval with large dataset."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Simulate large dataset
            mock_categories = [{'categoryId': i, 'categoryName': f'Category {i}'} for i in range(1, 501)]
            mock_service.get_all_categories.return_value = mock_categories
            
            service = get_category_service_for_testing()
            result = await service.get_all_categories(max_pages=20)
            
            assert len(result) == 500
            assert result[0]['categoryName'] == 'Category 1'
            assert result[-1]['categoryName'] == 'Category 500'
    
    def test_get_category_service_singleton(self):
        """Test that get_category_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.CategoryService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.get_all_categories_tool as tool_module
                    tool_module._category_service = None
                    
                    # Get service twice
                    service1 = get_category_service()
                    service2 = get_category_service()
                    
                    # Should be the same instance
                    assert service1 is service2
                    
                    # Config and client should only be called once
                    mock_config.assert_called_once()
                    mock_client.assert_called_once()
                    mock_service.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_categories_default_parameters(self):
        """Test retrieval with default parameters."""
        with patch('src.albatross_kiotviet_mcp.tools.get_all_categories_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_service.get_all_categories.return_value = []
            
            service = get_category_service_for_testing()
            await service.get_all_categories()
            
            # Verify service was called with default parameters
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=10
            )
