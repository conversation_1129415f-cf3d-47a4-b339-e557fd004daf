"""Unit tests for get_invoice_statistics_tool."""

import pytest
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool import (
    get_invoice_service_for_testing,
    get_invoice_service
)


class TestGetInvoiceStatisticsTool:
    """Test cases for get_invoice_statistics tool."""
    
    @pytest.mark.asyncio
    async def test_get_invoice_statistics_success(self):
        """Test successful invoice statistics calculation."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock responses
            mock_invoice_data = [
                {'id': 1, 'code': 'HD001', 'total': 100000, 'totalPayment': 100000},
                {'id': 2, 'code': 'HD002', 'total': 200000, 'totalPayment': 150000},
                {'id': 3, 'code': 'HD003', 'total': 300000, 'totalPayment': 0}
            ]
            
            mock_invoice_response = {
                'data': mock_invoice_data,
                'summary': {
                    'total_invoices': 3,
                    'total_sum': 600000,
                    'total_payment_sum': 250000,
                    'date_range': {
                        'from': '2025-07-20T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            
            mock_statistics = {
                'count': 3,
                'total_amount': 600000,
                'total_payment': 250000,
                'average_amount': 200000,
                'paid_invoices': 2,
                'unpaid_invoices': 1
            }
            
            mock_service.get_invoices_by_date_range.return_value = mock_invoice_response
            mock_service.calculate_invoice_statistics.return_value = mock_statistics
            
            # Test the service function directly
            service = get_invoice_service_for_testing()
            
            # First get the invoice data
            invoice_result = await service.get_invoices_by_date_range(
                from_date="2025-07-20",
                to_date=None,
                include_payment=True,
                include_invoice_delivery=False,
                current_item=0,
                page_size=100
            )
            
            # Then calculate statistics
            invoices = invoice_result.get('data', [])
            statistics = service.calculate_invoice_statistics(invoices)
            
            # Verify the results
            assert statistics['count'] == 3
            assert statistics['total_amount'] == 600000
            assert statistics['total_payment'] == 250000
            assert statistics['average_amount'] == 200000
            assert statistics['paid_invoices'] == 2
            assert statistics['unpaid_invoices'] == 1
            
            # Verify service calls
            mock_service.get_invoices_by_date_range.assert_called_once_with(
                from_date="2025-07-20",
                to_date=None,
                include_payment=True,
                include_invoice_delivery=False,
                current_item=0,
                page_size=100
            )
            mock_service.calculate_invoice_statistics.assert_called_once_with(mock_invoice_data)
    
    @pytest.mark.asyncio
    async def test_get_invoice_statistics_empty_data(self):
        """Test statistics calculation with empty invoice data."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_invoice_response = {
                'data': [],
                'summary': {
                    'total_invoices': 0,
                    'total_sum': 0,
                    'total_payment_sum': 0,
                    'date_range': {
                        'from': '2025-07-20T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            
            mock_statistics = {
                'count': 0,
                'total_amount': 0,
                'total_payment': 0,
                'average_amount': 0,
                'paid_invoices': 0,
                'unpaid_invoices': 0
            }
            
            mock_service.get_invoices_by_date_range.return_value = mock_invoice_response
            mock_service.calculate_invoice_statistics.return_value = mock_statistics
            
            service = get_invoice_service_for_testing()
            
            # Get invoice data
            invoice_result = await service.get_invoices_by_date_range("2025-07-20")
            
            # Calculate statistics
            invoices = invoice_result.get('data', [])
            statistics = service.calculate_invoice_statistics(invoices)
            
            # Verify empty statistics
            assert statistics['count'] == 0
            assert statistics['total_amount'] == 0
            assert statistics['average_amount'] == 0
    
    @pytest.mark.asyncio
    async def test_get_invoice_statistics_api_error(self):
        """Test statistics calculation with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_invoices_by_date_range.side_effect = Exception("API Error")
            
            service = get_invoice_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_invoices_by_date_range("2025-07-20")
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_invoice_statistics_with_date_range(self):
        """Test statistics calculation with specific date range."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_invoice_response = {
                'data': [{'id': 1, 'total': 100000, 'totalPayment': 100000}],
                'summary': {
                    'total_invoices': 1,
                    'total_sum': 100000,
                    'total_payment_sum': 100000,
                    'date_range': {
                        'from': '2025-07-19T00:00:00',
                        'to': '2025-07-20T23:59:59'
                    }
                }
            }
            
            mock_statistics = {
                'count': 1,
                'total_amount': 100000,
                'total_payment': 100000,
                'average_amount': 100000,
                'paid_invoices': 1,
                'unpaid_invoices': 0
            }
            
            mock_service.get_invoices_by_date_range.return_value = mock_invoice_response
            mock_service.calculate_invoice_statistics.return_value = mock_statistics
            
            service = get_invoice_service_for_testing()
            
            # Test with date range
            result = await service.get_invoices_by_date_range(
                from_date="2025-07-19",
                to_date="2025-07-20",
                page_size=50
            )
            
            assert result['summary']['date_range']['from'] == '2025-07-19T00:00:00'
            assert result['summary']['date_range']['to'] == '2025-07-20T23:59:59'
    
    @pytest.mark.asyncio
    async def test_get_invoice_statistics_custom_page_size(self):
        """Test statistics calculation with custom page size."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_invoice_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_service.get_invoices_by_date_range.return_value = {
                'data': [],
                'summary': {'total_invoices': 0, 'total_sum': 0, 'total_payment_sum': 0}
            }
            mock_service.calculate_invoice_statistics.return_value = {'count': 0}
            
            service = get_invoice_service_for_testing()
            
            # Test with custom page size
            await service.get_invoices_by_date_range(
                from_date="2025-07-20",
                page_size=200
            )
            
            # Verify service was called with custom page size
            mock_service.get_invoices_by_date_range.assert_called_once_with(
                from_date="2025-07-20",
                to_date=None,
                include_payment=True,
                include_invoice_delivery=True,
                current_item=0,
                page_size=200
            )
    
    def test_get_invoice_service_singleton(self):
        """Test that get_invoice_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool.InvoiceService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.get_invoice_statistics_tool as tool_module
                    tool_module._invoice_service = None
                    
                    # Get service twice
                    service1 = get_invoice_service()
                    service2 = get_invoice_service()
                    
                    # Should be the same instance
                    assert service1 is service2
