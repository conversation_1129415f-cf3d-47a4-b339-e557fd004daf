"""Unit tests for build_category_tree_tool."""

import pytest
from unittest.mock import AsyncMock, patch

from src.albatross_kiotviet_mcp.tools.build_category_tree_tool import (
    get_category_service_for_testing,
    get_category_service
)


class TestBuildCategoryTreeTool:
    """Test cases for build_category_tree tool."""
    
    @pytest.mark.asyncio
    async def test_build_category_tree_with_all_categories(self):
        """Test building category tree with all categories."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock categories
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics', 'parentId': None},
                {'categoryId': 2, 'categoryName': 'Smartphones', 'parentId': 1},
                {'categoryId': 3, 'categoryName': 'Clothing', 'parentId': None}
            ]
            
            # Setup mock tree
            mock_tree = {
                'root_categories': [
                    {
                        'categoryId': 1,
                        'categoryName': 'Electronics',
                        'children': [{'categoryId': 2, 'categoryName': 'Smartphones'}]
                    },
                    {'categoryId': 3, 'categoryName': 'Clothing'}
                ],
                'total_categories': 3,
                'tree_depth': 2
            }
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.build_category_tree.return_value = mock_tree
            
            # Test the service functions directly
            service = get_category_service_for_testing()
            
            # Get all categories
            categories = await service.get_all_categories(
                hierarchical_data=False,
                max_pages=10
            )
            
            # Build tree
            tree = service.build_category_tree(categories)
            
            # Verify the results
            assert tree['total_categories'] == 3
            assert tree['tree_depth'] == 2
            assert len(tree['root_categories']) == 2
            assert tree['root_categories'][0]['categoryName'] == 'Electronics'
            assert 'children' in tree['root_categories'][0]
            
            # Verify service calls
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=10
            )
            mock_service.build_category_tree.assert_called_once_with(mock_categories)
    
    @pytest.mark.asyncio
    async def test_build_category_tree_first_page_only(self):
        """Test building category tree with first page only."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock response for get_categories
            mock_categories_response = {
                'data': [
                    {'categoryId': 1, 'categoryName': 'Electronics', 'parentId': None},
                    {'categoryId': 2, 'categoryName': 'Clothing', 'parentId': None}
                ]
            }
            
            mock_tree = {
                'root_categories': [
                    {'categoryId': 1, 'categoryName': 'Electronics'},
                    {'categoryId': 2, 'categoryName': 'Clothing'}
                ],
                'total_categories': 2,
                'tree_depth': 1
            }
            
            mock_service.get_categories.return_value = mock_categories_response
            mock_service.build_category_tree.return_value = mock_tree
            
            service = get_category_service_for_testing()
            
            # Get first page categories
            result = await service.get_categories(
                page_size=100,
                hierarchical_data=False
            )
            categories = result.get('data', [])
            
            # Build tree
            tree = service.build_category_tree(categories)
            
            # Verify the results
            assert tree['total_categories'] == 2
            assert tree['tree_depth'] == 1
            assert len(tree['root_categories']) == 2
            
            # Verify service calls
            mock_service.get_categories.assert_called_once_with(
                page_size=100,
                hierarchical_data=False
            )
            mock_service.build_category_tree.assert_called_once_with(mock_categories_response['data'])
    
    @pytest.mark.asyncio
    async def test_build_category_tree_empty_categories(self):
        """Test building category tree with empty categories."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_tree = {
                'root_categories': [],
                'total_categories': 0,
                'tree_depth': 0
            }
            
            mock_service.get_all_categories.return_value = []
            mock_service.build_category_tree.return_value = mock_tree
            
            service = get_category_service_for_testing()
            
            # Get empty categories
            categories = await service.get_all_categories()
            
            # Build tree
            tree = service.build_category_tree(categories)
            
            # Verify empty tree
            assert tree['total_categories'] == 0
            assert tree['tree_depth'] == 0
            assert tree['root_categories'] == []
    
    @pytest.mark.asyncio
    async def test_build_category_tree_api_error(self):
        """Test building category tree with API error."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup mock to raise exception
            mock_service.get_all_categories.side_effect = Exception("API Error")
            
            service = get_category_service_for_testing()
            
            # Verify exception is raised
            with pytest.raises(Exception) as exc_info:
                await service.get_all_categories()
            
            assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_build_category_tree_custom_max_pages(self):
        """Test building category tree with custom max_pages."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            mock_categories = [{'categoryId': 1, 'categoryName': 'Test'}]
            mock_tree = {'root_categories': [], 'total_categories': 1, 'tree_depth': 1}
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.build_category_tree.return_value = mock_tree
            
            service = get_category_service_for_testing()
            
            # Test with custom max_pages
            categories = await service.get_all_categories(max_pages=5)
            tree = service.build_category_tree(categories)
            
            # Verify service was called with custom max_pages
            mock_service.get_all_categories.assert_called_once_with(
                hierarchical_data=False,
                max_pages=5
            )
    
    @pytest.mark.asyncio
    async def test_build_category_tree_deep_hierarchy(self):
        """Test building category tree with deep hierarchy."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_category_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_get_service.return_value = mock_service
            
            # Setup deep hierarchy
            mock_categories = [
                {'categoryId': 1, 'categoryName': 'Electronics', 'parentId': None},
                {'categoryId': 2, 'categoryName': 'Computers', 'parentId': 1},
                {'categoryId': 3, 'categoryName': 'Laptops', 'parentId': 2},
                {'categoryId': 4, 'categoryName': 'Gaming Laptops', 'parentId': 3}
            ]
            
            mock_tree = {
                'root_categories': [
                    {
                        'categoryId': 1,
                        'categoryName': 'Electronics',
                        'children': [
                            {
                                'categoryId': 2,
                                'categoryName': 'Computers',
                                'children': [
                                    {
                                        'categoryId': 3,
                                        'categoryName': 'Laptops',
                                        'children': [
                                            {'categoryId': 4, 'categoryName': 'Gaming Laptops'}
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ],
                'total_categories': 4,
                'tree_depth': 4
            }
            
            mock_service.get_all_categories.return_value = mock_categories
            mock_service.build_category_tree.return_value = mock_tree
            
            service = get_category_service_for_testing()
            
            categories = await service.get_all_categories()
            tree = service.build_category_tree(categories)
            
            # Verify deep hierarchy
            assert tree['tree_depth'] == 4
            assert tree['total_categories'] == 4
            assert len(tree['root_categories']) == 1
    
    def test_get_category_service_singleton(self):
        """Test that get_category_service returns the same instance."""
        with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.get_config') as mock_config:
            with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.KiotVietAPIClient') as mock_client:
                with patch('src.albatross_kiotviet_mcp.tools.build_category_tree_tool.CategoryService') as mock_service:
                    
                    # Clear the global service instance
                    import src.albatross_kiotviet_mcp.tools.build_category_tree_tool as tool_module
                    tool_module._category_service = None
                    
                    # Get service twice
                    service1 = get_category_service()
                    service2 = get_category_service()
                    
                    # Should be the same instance
                    assert service1 is service2
