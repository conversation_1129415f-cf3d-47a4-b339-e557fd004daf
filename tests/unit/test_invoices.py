"""Unit tests for invoice business logic."""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

from src.albatross_kiotviet_mcp.core.invoices import InvoiceService


class TestInvoiceService:
    """Test cases for InvoiceService."""
    
    @pytest.fixture
    def mock_client(self):
        """Create a mock KiotViet API client."""
        client = AsyncMock()
        client.__aenter__ = AsyncMock(return_value=client)
        client.__aexit__ = AsyncMock(return_value=None)
        return client
    
    @pytest.fixture
    def invoice_service(self, mock_client):
        """Create an InvoiceService instance with mock client."""
        return InvoiceService(mock_client)
    
    def test_format_date_range_simple_date(self, invoice_service):
        """Test date formatting with simple date strings."""
        from_date, to_date = invoice_service._format_date_range("2025-07-20")
        
        assert from_date == "2025-07-20T00:00:00"
        assert to_date == "2025-07-20T23:59:59"
    
    def test_format_date_range_with_to_date(self, invoice_service):
        """Test date formatting with both from and to dates."""
        from_date, to_date = invoice_service._format_date_range(
            "2025-07-20", "2025-07-21"
        )
        
        assert from_date == "2025-07-20T00:00:00"
        assert to_date == "2025-07-21T23:59:59"
    
    def test_format_date_range_iso_format(self, invoice_service):
        """Test date formatting with ISO format dates."""
        from_date, to_date = invoice_service._format_date_range(
            "2025-07-20T10:00:00", "2025-07-20T15:00:00"
        )
        
        assert from_date == "2025-07-20T10:00:00"
        assert to_date == "2025-07-20T15:00:00"
    
    def test_format_date_range_invalid_date(self, invoice_service):
        """Test date formatting with invalid date."""
        with pytest.raises(ValueError):
            invoice_service._format_date_range("invalid-date")
    
    def test_enhance_invoice_result(self, invoice_service, sample_invoice_data):
        """Test invoice result enhancement with summary."""
        result = {'data': sample_invoice_data}
        
        enhanced = invoice_service._enhance_invoice_result(
            result, "2025-07-20T00:00:00", "2025-07-20T23:59:59"
        )
        
        assert 'summary' in enhanced
        summary = enhanced['summary']
        
        assert summary['total_invoices'] == 3
        assert summary['total_sum'] == 600000  # 100k + 200k + 300k
        assert summary['total_payment_sum'] == 250000  # 100k + 150k + 0
        assert summary['date_range']['from'] == "2025-07-20T00:00:00"
        assert summary['date_range']['to'] == "2025-07-20T23:59:59"
    
    def test_calculate_invoice_statistics_empty(self, invoice_service):
        """Test statistics calculation with empty invoice list."""
        stats = invoice_service.calculate_invoice_statistics([])
        
        assert stats['count'] == 0
        assert stats['total_amount'] == 0
        assert stats['total_payment'] == 0
        assert stats['average_amount'] == 0
        assert stats['paid_invoices'] == 0
        assert stats['unpaid_invoices'] == 0
    
    def test_calculate_invoice_statistics(self, invoice_service, sample_invoice_data):
        """Test statistics calculation with sample data."""
        stats = invoice_service.calculate_invoice_statistics(sample_invoice_data)
        
        assert stats['count'] == 3
        assert stats['total_amount'] == 600000
        assert stats['total_payment'] == 250000
        assert stats['average_amount'] == 200000  # 600k / 3
        assert stats['paid_invoices'] == 2  # HD001 and HD002 have payments
        assert stats['unpaid_invoices'] == 1  # HD003 has no payment
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_range_success(self, invoice_service, mock_client, sample_invoice_data):
        """Test successful invoice retrieval by date range."""
        # Setup mock response
        mock_response = {
            'data': sample_invoice_data,
            'total': 3,
            'pageSize': 50
        }
        mock_client.get_invoices.return_value = mock_response
        
        # Call the method
        result = await invoice_service.get_invoices_by_date_range("2025-07-20")
        
        # Verify the result
        assert 'data' in result
        assert 'summary' in result
        assert result['summary']['total_invoices'] == 3
        assert result['summary']['total_sum'] == 600000
        
        # Verify client was called correctly
        mock_client.get_invoices.assert_called_once()
        call_args = mock_client.get_invoices.call_args[1]
        assert call_args['from_purchase_date'] == "2025-07-20T00:00:00"
        assert call_args['to_purchase_date'] == "2025-07-20T23:59:59"
    
    @pytest.mark.asyncio
    async def test_get_invoices_by_date_range_api_error(self, invoice_service, mock_client):
        """Test invoice retrieval with API error."""
        # Setup mock to raise exception
        mock_client.get_invoices.side_effect = Exception("API Error")
        
        # Verify exception is raised
        with pytest.raises(Exception) as exc_info:
            await invoice_service.get_invoices_by_date_range("2025-07-20")
        
        assert "Failed to retrieve invoices" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_invoice_by_id_not_implemented(self, invoice_service):
        """Test that get_invoice_by_id raises NotImplementedError."""
        with pytest.raises(Exception) as exc_info:
            await invoice_service.get_invoice_by_id("HD001")
        
        assert "not yet implemented" in str(exc_info.value)
