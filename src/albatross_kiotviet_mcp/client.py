"""KiotViet API client with improved error handling and structure."""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from enum import Enum
import httpx
from .config import KiotVietConfig
from .auth import TokenManager

logger = logging.getLogger(__name__)


class KiotVietAPIError(Exception):
    """Base exception for KiotViet API errors."""
    pass


class AuthenticationError(KiotVietAPIError):
    """Raised when authentication fails."""
    pass


class ValidationError(KiotVietAPIError):
    """Raised when request parameters are invalid."""
    pass


class RateLimitError(KiotVietAPIError):
    """Raised when API rate limit is exceeded."""
    pass


class APIConnectionError(KiotVietAPIError):
    """Raised when connection to API fails."""
    pass


class OrderDirection(str, Enum):
    """Valid order directions for API requests."""
    ASC = "Asc"
    DESC = "Desc"


class APIConstants:
    """Constants for API limits and defaults."""
    MAX_PAGE_SIZE = 100
    MIN_PAGE_SIZE = 1
    DEFAULT_PAGE_SIZE = 50
    DEFAULT_CURRENT_ITEM = 0
    MAX_RETRIES = 3
    RETRY_BASE_DELAY = 2


class KiotVietAPIClient:
    """Enhanced client for interacting with KiotViet Public API.

    This client provides:
    - Robust error handling with custom exceptions
    - Parameter validation
    - Structured logging
    - Configurable retry strategies
    - Clean separation of concerns
    """

    def __init__(self, config: KiotVietConfig):
        """Initialize the KiotViet API client.

        Args:
            config: Configuration object containing API credentials and settings
        """
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            base_url=self.config.api_base_url,
            timeout=self.config.request_timeout
        )
        self._logger.debug("HTTP client initialized")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
            self._logger.debug("HTTP client closed")
        # Return None to not suppress exceptions
        return None

    async def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests.

        Returns:
            Dictionary containing required headers for API authentication

        Raises:
            AuthenticationError: If token retrieval fails
        """
        try:
            access_token = await self.token_manager.get_access_token()
            return {
                "Retailer": self.config.retailer,
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        except Exception as e:
            self._logger.error(f"Failed to get authentication headers: {e}")
            raise AuthenticationError(f"Authentication failed: {e}") from e

    def _validate_pagination_params(
        self,
        page_size: int,
        current_item: int
    ) -> None:
        """Validate pagination parameters.

        Args:
            page_size: Number of items per page
            current_item: Starting item index

        Raises:
            ValidationError: If parameters are invalid
        """
        if not isinstance(page_size, int) or page_size < APIConstants.MIN_PAGE_SIZE or page_size > APIConstants.MAX_PAGE_SIZE:
            raise ValidationError(
                f"page_size must be an integer between {APIConstants.MIN_PAGE_SIZE} and {APIConstants.MAX_PAGE_SIZE}, got: {page_size}"
            )

        if not isinstance(current_item, int) or current_item < 0:
            raise ValidationError(
                f"current_item must be a non-negative integer, got: {current_item}"
            )

    def _validate_order_direction(self, order_direction: str) -> None:
        """Validate order direction parameter.

        Args:
            order_direction: Sort order direction

        Raises:
            ValidationError: If order direction is invalid
        """
        if not isinstance(order_direction, str) or order_direction not in [OrderDirection.ASC, OrderDirection.DESC]:
            raise ValidationError(
                f"order_direction must be '{OrderDirection.ASC}' or '{OrderDirection.DESC}', got: {order_direction}"
            )

    def _validate_date_range(self, from_date: str, to_date: str) -> None:
        """Validate date range parameters.

        Args:
            from_date: Start date string
            to_date: End date string

        Raises:
            ValidationError: If dates are invalid or in wrong order
        """
        if not isinstance(from_date, str) or not from_date.strip():
            raise ValidationError("from_purchase_date must be a non-empty string")

        if not isinstance(to_date, str) or not to_date.strip():
            raise ValidationError("to_purchase_date must be a non-empty string")

        # Basic format validation (could be enhanced with datetime parsing)
        if len(from_date) < 10 or len(to_date) < 10:
            raise ValidationError("Date strings must be in ISO format (e.g., '2025-07-19T00:00:00')")

    def _build_request_data(self, **kwargs) -> Dict[str, Any]:
        """Build request data dictionary from keyword arguments.

        Args:
            **kwargs: Request parameters

        Returns:
            Dictionary with non-None values
        """
        return {k: v for k, v in kwargs.items() if v is not None}


    # ============================================================================
    # PUBLIC API METHODS
    # ============================================================================

    async def get_categories(
        self,
        page_size: int = APIConstants.DEFAULT_PAGE_SIZE,
        current_item: int = APIConstants.DEFAULT_CURRENT_ITEM,
        order_direction: str = OrderDirection.ASC,
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (1-100, default: 50)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order ("Asc" or "Desc", default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)

        Returns:
            API response containing categories data

        Raises:
            ValidationError: If parameters are invalid
            APIConnectionError: If request fails
            AuthenticationError: If authentication fails
        """
        # Validate parameters
        self._validate_pagination_params(page_size, current_item)
        self._validate_order_direction(order_direction)

        # Build request data
        data = self._build_request_data(
            pageSize=min(page_size, APIConstants.MAX_PAGE_SIZE),
            currentItem=current_item,
            orderDirection=order_direction,
            hierachicalData=hierarchical_data  # Note: API uses this spelling
        )

        self._logger.info(
            f"Fetching categories: page_size={page_size}, current_item={current_item}, "
            f"order={order_direction}, hierarchical={hierarchical_data}"
        )

        return await self._make_request("GET", "/categories", data=data)

    async def get_invoices(
        self,
        from_purchase_date: str,
        to_purchase_date: str,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        current_item: int = APIConstants.DEFAULT_CURRENT_ITEM,
        page_size: int = APIConstants.DEFAULT_PAGE_SIZE
    ) -> Dict[str, Any]:
        """Get invoices from KiotViet API by date range.

        Args:
            from_purchase_date: Start date in ISO format (e.g., "2025-07-19T00:00:00")
            to_purchase_date: End date in ISO format (e.g., "2025-07-19T23:59:59")
            include_payment: Whether to include payment information (default: True)
            include_invoice_delivery: Whether to include delivery information (default: True)
            current_item: Starting item index for pagination (default: 0)
            page_size: Number of items per page (1-100, default: 50)

        Returns:
            API response containing invoices data

        Raises:
            ValidationError: If parameters are invalid
            APIConnectionError: If request fails
            AuthenticationError: If authentication fails
        """
        # Validate parameters
        self._validate_pagination_params(page_size, current_item)
        self._validate_date_range(from_purchase_date, to_purchase_date)

        # Build request data
        data = self._build_request_data(
            includePayment=include_payment,
            includeInvoiceDelivery=include_invoice_delivery,
            currentItem=current_item,
            pageSize=min(page_size, APIConstants.MAX_PAGE_SIZE),
            fromPurchaseDate=from_purchase_date,
            toPurchaseDate=to_purchase_date
        )

        self._logger.info(
            f"Fetching invoices: date_range={from_purchase_date} to {to_purchase_date}, "
            f"page_size={page_size}, current_item={current_item}"
        )

        return await self._make_request("GET", "/invoices", data=data)


    # ============================================================================
    # INTERNAL METHODS
    # ============================================================================

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an authenticated request to KiotViet API with robust error handling.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: Request body data (for JSON requests)
            params: URL query parameters

        Returns:
            Parsed JSON response from the API

        Raises:
            APIConnectionError: If client is not initialized or connection fails
            AuthenticationError: If authentication fails after retry
            RateLimitError: If rate limit is exceeded
            KiotVietAPIError: For other API errors
        """
        if not self._client:
            raise APIConnectionError("Client not initialized. Use async context manager.")

        url = endpoint if endpoint.startswith('http') else f"{self.config.api_base_url}{endpoint}"
        max_retries = getattr(self.config, 'max_retries', APIConstants.MAX_RETRIES)

        self._logger.debug(f"Making {method} request to {endpoint}")

        for attempt in range(max_retries):
            try:
                headers = await self._get_headers()

                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                )

                # Handle specific HTTP status codes
                if response.status_code == 401:
                    if attempt == 0:
                        self._logger.warning("Received 401 Unauthorized, invalidating token and retrying...")
                        self.token_manager.invalidate_token()
                        continue
                    else:
                        raise AuthenticationError("Authentication failed after token refresh")

                elif response.status_code == 429:
                    self._logger.warning(f"Rate limit exceeded (attempt {attempt + 1})")
                    if attempt == max_retries - 1:
                        raise RateLimitError("API rate limit exceeded")
                    await self._wait_for_retry(attempt)
                    continue

                # Raise for other HTTP errors
                response.raise_for_status()

                # Parse and return JSON response
                result = response.json()
                self._logger.debug(f"Request successful: {method} {endpoint}")
                return result

            except httpx.TimeoutException as e:
                self._logger.error(f"Request timeout (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise APIConnectionError(f"Request timeout after {max_retries} attempts") from e
                await self._wait_for_retry(attempt)

            except httpx.NetworkError as e:
                self._logger.error(f"Network error (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise APIConnectionError(f"Network error after {max_retries} attempts") from e
                await self._wait_for_retry(attempt)

            except httpx.HTTPStatusError as e:
                self._logger.error(f"HTTP error {e.response.status_code} (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise KiotVietAPIError(f"API error {e.response.status_code}: {e}") from e
                await self._wait_for_retry(attempt)

            except Exception as e:
                self._logger.error(f"Unexpected error (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise KiotVietAPIError(f"Unexpected error after {max_retries} attempts: {e}") from e
                await self._wait_for_retry(attempt)

        # This should never be reached, but just in case
        raise KiotVietAPIError(f"Request failed after {max_retries} attempts")

    async def _wait_for_retry(self, attempt: int) -> None:
        """Wait before retrying a failed request using exponential backoff.

        Args:
            attempt: Current attempt number (0-based)
        """
        delay = APIConstants.RETRY_BASE_DELAY ** attempt
        self._logger.debug(f"Waiting {delay} seconds before retry...")
        await asyncio.sleep(delay)