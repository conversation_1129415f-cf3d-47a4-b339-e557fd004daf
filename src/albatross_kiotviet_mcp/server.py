"""KiotViet MCP Server.

This is the main server module that initializes the FastMCP server
and registers all available tools from the tools package.
"""

import asyncio
import logging
import os
from datetime import datetime
from fastmcp import FastMCP
from dotenv import load_dotenv

from .config import get_config
from .tools import register_all_tools

# Load environment variables
load_dotenv()

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
log_dir = os.path.join(project_root, 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, f'kiotviet_mcp_{datetime.now().strftime("%Y%m%d")}.log')
print(f"📁 Log file: {log_file}")  # Debug print

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
server = FastMCP(name="KiotVietMCPServer")

# Register all tools
register_all_tools(server)


def run():
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {server.name}...")
    
    # Validate configuration on startup
    try:
        config = get_config()
        logger.info(f"Configuration loaded successfully for retailer: {config.retailer}")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise
    
    return server.run()


if __name__ == "__main__":
    # For direct execution
    asyncio.run(run())