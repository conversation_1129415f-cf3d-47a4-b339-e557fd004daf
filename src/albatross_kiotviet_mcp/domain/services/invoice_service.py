"""Invoice domain service.

This service contains pure business logic for invoice operations.
It has no external dependencies and works with domain entities.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from decimal import Decimal
from ..entities.invoice import Invoice, InvoiceCollection
from ..exceptions import ValidationError


class InvoiceDomainService:
    """Domain service for invoice business logic."""
    
    def validate_pagination_params(self, page_size: int, current_item: int) -> None:
        """Validate pagination parameters.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            
        Raises:
            ValidationError: If parameters are invalid
        """
        if not isinstance(page_size, int) or page_size <= 0 or page_size > 100:
            raise ValidationError("page_size must be an integer between 1 and 100")
        
        if not isinstance(current_item, int) or current_item < 0:
            raise ValidationError("current_item must be a non-negative integer")
    
    def format_date_range(self, from_date: str, to_date: Optional[str] = None) -> <PERSON><PERSON>[str, str]:
        """Format and validate date range for API requests.
        
        Args:
            from_date: Start date in format "2025-07-19" or "2025-07-19T00:00:00"
            to_date: End date (optional, defaults to same day as from_date)
            
        Returns:
            Tuple of (formatted_from_date, formatted_to_date)
            
        Raises:
            ValidationError: If date format is invalid
        """
        if not from_date or not from_date.strip():
            raise ValidationError("from_date cannot be empty")
        
        from_date = from_date.strip()
        
        # Handle from_date formatting
        if 'T' not in from_date:
            from_purchase_date = f"{from_date}T00:00:00"
        else:
            from_purchase_date = from_date
            
        # Handle to_date
        if to_date is None:
            # Use same day as from_date
            date_part = from_date.split('T')[0] if 'T' in from_date else from_date
            to_purchase_date = f"{date_part}T23:59:59"
        else:
            to_date = to_date.strip()
            if 'T' not in to_date:
                to_purchase_date = f"{to_date}T23:59:59"
            else:
                to_purchase_date = to_date
        
        # Validate date formats
        try:
            datetime.fromisoformat(from_purchase_date.replace('T', ' '))
            datetime.fromisoformat(to_purchase_date.replace('T', ' '))
        except ValueError as e:
            raise ValidationError(f"Invalid date format: {e}")
        
        return from_purchase_date, to_purchase_date
    
    def create_invoice_collection_from_data(self, invoice_data: List[Dict[str, Any]]) -> InvoiceCollection:
        """Create an invoice collection from raw data.
        
        Args:
            invoice_data: List of invoice dictionaries from API
            
        Returns:
            InvoiceCollection: Collection of invoice entities
        """
        invoices = []
        for data in invoice_data:
            try:
                invoice = Invoice.from_dict(data)
                invoices.append(invoice)
            except (KeyError, ValueError, ValidationError) as e:
                # Log the error but continue processing other invoices
                # In a real application, you might want to handle this differently
                continue
        
        return InvoiceCollection(invoices)
    
    def enhance_invoice_result(
        self, 
        invoices: InvoiceCollection, 
        original_result: Dict[str, Any],
        from_date: str,
        to_date: str
    ) -> Dict[str, Any]:
        """Enhance invoice result with calculated totals and summary.
        
        Args:
            invoices: Invoice collection
            original_result: Original API result
            from_date: Formatted start date
            to_date: Formatted end date
            
        Returns:
            Enhanced result with summary information
        """
        enhanced_result = original_result.copy()
        
        # Add comprehensive summary
        enhanced_result['summary'] = {
            'total_invoices': invoices.get_total_count(),
            'total_sum': float(invoices.get_total_sum()),
            'total_payment_sum': float(invoices.get_total_payment_sum()),
            'outstanding_sum': float(invoices.get_outstanding_sum()),
            'fully_paid_count': invoices.get_fully_paid_count(),
            'unpaid_count': invoices.get_unpaid_count(),
            'partially_paid_count': invoices.get_partially_paid_count(),
            'date_range': {
                'from': from_date,
                'to': to_date
            }
        }
        
        return enhanced_result
    
    def calculate_invoice_statistics(self, invoices: InvoiceCollection) -> Dict[str, Any]:
        """Calculate comprehensive statistics for invoices.
        
        Args:
            invoices: Invoice collection
            
        Returns:
            Dictionary containing various statistics
        """
        if invoices.get_total_count() == 0:
            return {
                'count': 0,
                'total_amount': 0.0,
                'total_payment': 0.0,
                'outstanding_amount': 0.0,
                'average_amount': 0.0,
                'average_payment': 0.0,
                'payment_percentage': 0.0,
                'fully_paid_count': 0,
                'unpaid_count': 0,
                'partially_paid_count': 0,
                'date_range': None
            }
        
        total_sum = invoices.get_total_sum()
        total_payment_sum = invoices.get_total_payment_sum()
        count = invoices.get_total_count()
        
        return {
            'count': count,
            'total_amount': float(total_sum),
            'total_payment': float(total_payment_sum),
            'outstanding_amount': float(invoices.get_outstanding_sum()),
            'average_amount': float(total_sum / count),
            'average_payment': float(total_payment_sum / count),
            'payment_percentage': float((total_payment_sum / total_sum * 100) if total_sum > 0 else 0),
            'fully_paid_count': invoices.get_fully_paid_count(),
            'unpaid_count': invoices.get_unpaid_count(),
            'partially_paid_count': invoices.get_partially_paid_count(),
            'date_range': {
                'from': invoices.get_date_range()[0].isoformat() if invoices.get_date_range() else None,
                'to': invoices.get_date_range()[1].isoformat() if invoices.get_date_range() else None
            } if invoices.get_date_range() else None
        }
    
    def filter_invoices_by_payment_status(
        self, 
        invoices: InvoiceCollection, 
        status: str
    ) -> InvoiceCollection:
        """Filter invoices by payment status.
        
        Args:
            invoices: Invoice collection to filter
            status: Payment status ('paid', 'unpaid', 'partial')
            
        Returns:
            Filtered invoice collection
            
        Raises:
            ValidationError: If status is invalid
        """
        return invoices.filter_by_payment_status(status)
    
    def convert_invoices_to_dict_list(self, invoices: InvoiceCollection) -> List[Dict[str, Any]]:
        """Convert invoice collection to dictionary list.
        
        Args:
            invoices: Invoice collection
            
        Returns:
            List of invoice dictionaries
        """
        return invoices.to_dict_list()
    
    def validate_date_range_order(self, from_date: str, to_date: str) -> None:
        """Validate that from_date is before or equal to to_date.
        
        Args:
            from_date: Start date string
            to_date: End date string
            
        Raises:
            ValidationError: If date order is invalid
        """
        try:
            from_dt = datetime.fromisoformat(from_date.replace('T', ' '))
            to_dt = datetime.fromisoformat(to_date.replace('T', ' '))
            
            if from_dt > to_dt:
                raise ValidationError("from_date must be before or equal to to_date")
                
        except ValueError as e:
            raise ValidationError(f"Invalid date format: {e}")
