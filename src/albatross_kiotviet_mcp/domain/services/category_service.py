"""Category domain service.

This service contains pure business logic for category operations.
It has no external dependencies and works with domain entities.
"""

from typing import List, Dict, Any, Optional
from ..entities.category import Category, CategoryTree
from ..exceptions import ValidationError


class CategoryDomainService:
    """Domain service for category business logic."""
    
    def validate_pagination_params(self, page_size: int, current_item: int) -> None:
        """Validate pagination parameters.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            
        Raises:
            ValidationError: If parameters are invalid
        """
        if not isinstance(page_size, int) or page_size <= 0 or page_size > 100:
            raise ValidationError("page_size must be an integer between 1 and 100")
        
        if not isinstance(current_item, int) or current_item < 0:
            raise ValidationError("current_item must be a non-negative integer")
    
    def validate_order_direction(self, order_direction: str) -> None:
        """Validate order direction parameter.
        
        Args:
            order_direction: Sort order direction
            
        Raises:
            ValidationError: If order direction is invalid
        """
        valid_directions = ["Asc", "Desc"]
        if not isinstance(order_direction, str) or order_direction not in valid_directions:
            raise ValidationError(f"order_direction must be one of {valid_directions}")
    
    def build_category_tree_from_flat_data(self, category_data: List[Dict[str, Any]]) -> CategoryTree:
        """Build a category tree from flat category data.
        
        Args:
            category_data: List of category dictionaries from API
            
        Returns:
            CategoryTree: Organized category tree
        """
        # Convert dictionaries to Category entities
        categories = []
        category_map = {}
        
        # First pass: create all categories
        for data in category_data:
            category = Category.from_dict(data)
            categories.append(category)
            category_map[category.category_id] = category
        
        # Second pass: build parent-child relationships
        for category in categories:
            if category.parent_id is not None and category.parent_id in category_map:
                parent = category_map[category.parent_id]
                parent.add_child(category)
        
        return CategoryTree(categories)
    
    def search_categories_by_name(
        self, 
        categories: List[Category], 
        search_term: str,
        case_sensitive: bool = False
    ) -> List[Category]:
        """Search categories by name.
        
        Args:
            categories: List of categories to search
            search_term: Term to search for
            case_sensitive: Whether search should be case sensitive
            
        Returns:
            List of matching categories
        """
        if not search_term or not search_term.strip():
            return []
        
        results = []
        search_term = search_term.strip()
        
        for category in categories:
            if category.is_root_category():  # Only search from root to avoid duplicates
                results.extend(category.search_by_name(search_term, case_sensitive))
        
        return results
    
    def enhance_category_result(
        self, 
        categories: List[Category], 
        original_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhance category result with additional metadata.
        
        Args:
            categories: List of category entities
            original_result: Original API result
            
        Returns:
            Enhanced result with metadata
        """
        category_tree = CategoryTree(categories)
        
        enhanced_result = original_result.copy()
        enhanced_result['metadata'] = {
            'total_categories': category_tree.get_total_count(),
            'has_subcategories': category_tree.has_subcategories(),
            'category_names': category_tree.get_category_names(),
            'max_depth': category_tree.get_max_depth()
        }
        
        return enhanced_result
    
    def convert_categories_to_dict_list(self, categories: List[Category]) -> List[Dict[str, Any]]:
        """Convert category entities to dictionary list.
        
        Args:
            categories: List of category entities
            
        Returns:
            List of category dictionaries
        """
        return [category.to_dict() for category in categories]
    
    def filter_categories_by_parent(
        self, 
        categories: List[Category], 
        parent_id: Optional[int]
    ) -> List[Category]:
        """Filter categories by parent ID.
        
        Args:
            categories: List of categories to filter
            parent_id: Parent ID to filter by (None for root categories)
            
        Returns:
            Filtered list of categories
        """
        return [cat for cat in categories if cat.parent_id == parent_id]
    
    def get_category_statistics(self, categories: List[Category]) -> Dict[str, Any]:
        """Get comprehensive statistics for categories.
        
        Args:
            categories: List of category entities
            
        Returns:
            Dictionary containing category statistics
        """
        category_tree = CategoryTree(categories)
        root_categories = category_tree.root_categories
        
        return {
            'total_categories': category_tree.get_total_count(),
            'root_categories_count': len(root_categories),
            'leaf_categories_count': sum(1 for cat in categories if cat.is_leaf_category()),
            'max_depth': category_tree.get_max_depth(),
            'has_subcategories': category_tree.has_subcategories(),
            'category_names': category_tree.get_category_names()
        }
