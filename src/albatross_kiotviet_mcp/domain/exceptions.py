"""Domain-specific exceptions.

These exceptions represent business rule violations and domain errors.
They should not depend on any external libraries or infrastructure concerns.
"""


class DomainError(Exception):
    """Base exception for all domain errors."""
    pass


class ValidationError(DomainError):
    """Raised when domain validation rules are violated."""
    pass


class BusinessRuleError(DomainError):
    """Raised when business rules are violated."""
    pass


class EntityNotFoundError(DomainError):
    """Raised when a requested entity cannot be found."""
    pass


class InvalidOperationError(DomainError):
    """Raised when an operation is not valid in the current context."""
    pass
