"""Invoice domain entity.

This module contains the Invoice domain entity with business logic
and validation rules. It has no external dependencies.
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

from ..exceptions import ValidationError


@dataclass
class Invoice:
    """Invoice domain entity representing a sales invoice."""
    
    invoice_id: int
    code: str
    total: Decimal
    total_payment: Decimal
    purchase_date: datetime
    
    def __post_init__(self):
        """Validate the invoice after initialization."""
        self.validate()
    
    def validate(self) -> None:
        """Validate invoice business rules."""
        if not self.code or not self.code.strip():
            raise ValidationError("Invoice code cannot be empty")
        
        if self.invoice_id <= 0:
            raise ValidationError("Invoice ID must be positive")
        
        if self.total < 0:
            raise ValidationError("Invoice total cannot be negative")
        
        if self.total_payment < 0:
            raise ValidationError("Total payment cannot be negative")
        
        if self.total_payment > self.total:
            raise ValidationError("Total payment cannot exceed invoice total")
    
    def is_fully_paid(self) -> bool:
        """Check if the invoice is fully paid."""
        return self.total_payment >= self.total
    
    def is_unpaid(self) -> bool:
        """Check if the invoice is completely unpaid."""
        return self.total_payment == 0
    
    def is_partially_paid(self) -> bool:
        """Check if the invoice is partially paid."""
        return 0 < self.total_payment < self.total
    
    def get_outstanding_amount(self) -> Decimal:
        """Get the outstanding amount (unpaid portion)."""
        return max(Decimal('0'), self.total - self.total_payment)
    
    def get_payment_percentage(self) -> float:
        """Get the payment percentage (0-100)."""
        if self.total == 0:
            return 100.0
        return float((self.total_payment / self.total) * 100)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert invoice to dictionary representation."""
        return {
            'id': self.invoice_id,
            'code': self.code,
            'total': float(self.total),
            'totalPayment': float(self.total_payment),
            'purchaseDate': self.purchase_date.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Invoice':
        """Create invoice from dictionary representation."""
        # Handle different possible date formats
        purchase_date = data['purchaseDate']
        if isinstance(purchase_date, str):
            # Try to parse ISO format
            try:
                purchase_date = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
            except ValueError:
                # Fallback to basic parsing
                purchase_date = datetime.fromisoformat(purchase_date)
        
        return cls(
            invoice_id=data['id'],
            code=data['code'],
            total=Decimal(str(data['total'])),
            total_payment=Decimal(str(data['totalPayment'])),
            purchase_date=purchase_date
        )
    
    def __str__(self) -> str:
        """String representation of the invoice."""
        return f"Invoice(id={self.invoice_id}, code='{self.code}', total={self.total})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the invoice."""
        return (f"Invoice(invoice_id={self.invoice_id}, code='{self.code}', "
                f"total={self.total}, total_payment={self.total_payment}, "
                f"purchase_date={self.purchase_date})")


class InvoiceCollection:
    """Represents a collection of invoices with aggregate operations."""
    
    def __init__(self, invoices: List[Invoice]):
        """Initialize the invoice collection."""
        self.invoices = invoices
    
    def get_total_count(self) -> int:
        """Get total number of invoices."""
        return len(self.invoices)
    
    def get_total_sum(self) -> Decimal:
        """Get sum of all invoice totals."""
        return sum(invoice.total for invoice in self.invoices)
    
    def get_total_payment_sum(self) -> Decimal:
        """Get sum of all payments."""
        return sum(invoice.total_payment for invoice in self.invoices)
    
    def get_outstanding_sum(self) -> Decimal:
        """Get sum of all outstanding amounts."""
        return sum(invoice.get_outstanding_amount() for invoice in self.invoices)
    
    def get_fully_paid_count(self) -> int:
        """Get count of fully paid invoices."""
        return sum(1 for invoice in self.invoices if invoice.is_fully_paid())
    
    def get_unpaid_count(self) -> int:
        """Get count of unpaid invoices."""
        return sum(1 for invoice in self.invoices if invoice.is_unpaid())
    
    def get_partially_paid_count(self) -> int:
        """Get count of partially paid invoices."""
        return sum(1 for invoice in self.invoices if invoice.is_partially_paid())
    
    def get_date_range(self) -> Optional[tuple[datetime, datetime]]:
        """Get the date range of invoices (earliest, latest)."""
        if not self.invoices:
            return None
        
        dates = [invoice.purchase_date for invoice in self.invoices]
        return min(dates), max(dates)
    
    def filter_by_payment_status(self, status: str) -> 'InvoiceCollection':
        """Filter invoices by payment status."""
        if status == 'paid':
            filtered = [inv for inv in self.invoices if inv.is_fully_paid()]
        elif status == 'unpaid':
            filtered = [inv for inv in self.invoices if inv.is_unpaid()]
        elif status == 'partial':
            filtered = [inv for inv in self.invoices if inv.is_partially_paid()]
        else:
            raise ValidationError(f"Invalid payment status: {status}")
        
        return InvoiceCollection(filtered)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics for the invoice collection."""
        date_range = self.get_date_range()
        
        return {
            'total_invoices': self.get_total_count(),
            'total_sum': float(self.get_total_sum()),
            'total_payment_sum': float(self.get_total_payment_sum()),
            'outstanding_sum': float(self.get_outstanding_sum()),
            'fully_paid_count': self.get_fully_paid_count(),
            'unpaid_count': self.get_unpaid_count(),
            'partially_paid_count': self.get_partially_paid_count(),
            'date_range': {
                'from': date_range[0].isoformat() if date_range else None,
                'to': date_range[1].isoformat() if date_range else None
            } if date_range else None
        }
    
    def to_dict_list(self) -> List[Dict[str, Any]]:
        """Convert all invoices to dictionary list."""
        return [invoice.to_dict() for invoice in self.invoices]
