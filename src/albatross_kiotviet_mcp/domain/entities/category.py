"""Category domain entity.

This module contains the Category domain entity with business logic
and validation rules. It has no external dependencies.
"""

from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field

from ..exceptions import ValidationError


@dataclass
class Category:
    """Category domain entity representing a product category."""
    
    category_id: int
    category_name: str
    parent_id: Optional[int] = None
    has_child: bool = False
    children: List['Category'] = field(default_factory=list)
    
    def __post_init__(self):
        """Validate the category after initialization."""
        self.validate()
    
    def validate(self) -> None:
        """Validate category business rules."""
        if not self.category_name or not self.category_name.strip():
            raise ValidationError("Category name cannot be empty")
        
        if self.category_id <= 0:
            raise ValidationError("Category ID must be positive")
        
        if self.parent_id is not None and self.parent_id <= 0:
            raise ValidationError("Parent ID must be positive if provided")
        
        if self.parent_id == self.category_id:
            raise ValidationError("Category cannot be its own parent")
    
    def is_root_category(self) -> bool:
        """Check if this is a root category (no parent)."""
        return self.parent_id is None
    
    def is_leaf_category(self) -> bool:
        """Check if this is a leaf category (no children)."""
        return not self.has_child and len(self.children) == 0
    
    def add_child(self, child: 'Category') -> None:
        """Add a child category."""
        if child.parent_id != self.category_id:
            raise ValidationError("Child category parent_id must match this category's ID")
        
        # Check if child already exists
        if any(c.category_id == child.category_id for c in self.children):
            return  # Already exists, don't add duplicate
        
        self.children.append(child)
        self.has_child = True
    
    def get_depth(self) -> int:
        """Calculate the maximum depth of the category tree from this node."""
        if not self.children:
            return 1
        
        max_child_depth = max(child.get_depth() for child in self.children)
        return 1 + max_child_depth
    
    def search_by_name(self, search_term: str, case_sensitive: bool = False) -> List['Category']:
        """Search for categories by name in this subtree."""
        results = []
        
        # Check current category
        category_name = self.category_name if case_sensitive else self.category_name.lower()
        search_term = search_term if case_sensitive else search_term.lower()
        
        if search_term in category_name:
            results.append(self)
        
        # Search children recursively
        for child in self.children:
            results.extend(child.search_by_name(search_term, case_sensitive))
        
        return results
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert category to dictionary representation."""
        return {
            'categoryId': self.category_id,
            'categoryName': self.category_name,
            'parentId': self.parent_id,
            'hasChild': self.has_child,
            'children': [child.to_dict() for child in self.children]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Category':
        """Create category from dictionary representation."""
        children_data = data.get('children', [])
        children = [cls.from_dict(child_data) for child_data in children_data]
        
        return cls(
            category_id=data['categoryId'],
            category_name=data['categoryName'],
            parent_id=data.get('parentId'),
            has_child=data.get('hasChild', False),
            children=children
        )
    
    def __str__(self) -> str:
        """String representation of the category."""
        return f"Category(id={self.category_id}, name='{self.category_name}', parent={self.parent_id})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the category."""
        return (f"Category(category_id={self.category_id}, category_name='{self.category_name}', "
                f"parent_id={self.parent_id}, has_child={self.has_child}, "
                f"children_count={len(self.children)})")


class CategoryTree:
    """Represents a collection of categories organized as a tree structure."""
    
    def __init__(self, categories: List[Category]):
        """Initialize the category tree."""
        self.categories = categories
        self._root_categories: Optional[List[Category]] = None
        self._category_map: Optional[Dict[int, Category]] = None
    
    @property
    def root_categories(self) -> List[Category]:
        """Get root categories (categories with no parent)."""
        if self._root_categories is None:
            self._root_categories = [cat for cat in self.categories if cat.is_root_category()]
        return self._root_categories
    
    @property
    def category_map(self) -> Dict[int, Category]:
        """Get a mapping of category ID to category."""
        if self._category_map is None:
            self._category_map = {cat.category_id: cat for cat in self.categories}
        return self._category_map
    
    def get_total_count(self) -> int:
        """Get total number of categories."""
        return len(self.categories)
    
    def get_max_depth(self) -> int:
        """Get maximum depth of the category tree."""
        if not self.root_categories:
            return 0
        
        return max(cat.get_depth() for cat in self.root_categories)
    
    def has_subcategories(self) -> bool:
        """Check if any category has subcategories."""
        return any(cat.has_child for cat in self.categories)
    
    def get_category_names(self) -> List[str]:
        """Get list of all category names."""
        return [cat.category_name for cat in self.categories]
    
    def search_categories(self, search_term: str, case_sensitive: bool = False) -> List[Category]:
        """Search for categories by name."""
        results = []
        for category in self.categories:
            if category.is_root_category():  # Only search from root to avoid duplicates
                results.extend(category.search_by_name(search_term, case_sensitive))
        return results
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert category tree to dictionary representation."""
        return {
            'root_categories': [cat.to_dict() for cat in self.root_categories],
            'total_categories': self.get_total_count(),
            'tree_depth': self.get_max_depth()
        }
