"""Category business logic service.

This module contains all category-related business logic separated from MCP concerns.
It can be tested independently and reused across different interfaces.
"""

import logging
from typing import Dict, Any, List, Optional

from ..client import KiotVietAPIClient

logger = logging.getLogger(__name__)


class CategoryService:
    """Service class for category-related business operations."""
    
    def __init__(self, client: KiotVietAPIClient):
        """Initialize the category service with a KiotViet API client.
        
        Args:
            client: Configured KiotViet API client instance
        """
        self.client = client
    
    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.
        
        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure
        
        Returns:
            Dictionary containing categories data and pagination info
        
        Raises:
            ValueError: If parameters are invalid
            Exception: If API request fails
        """
        logger.info(f"📂 Getting categories: page_size={page_size}, current_item={current_item}, hierarchical={hierarchical_data}")
        
        try:
            # Validate parameters
            self._validate_category_params(page_size, current_item, order_direction)
            
            # Make API request
            async with self.client:
                result = await self.client.get_categories(
                    page_size=min(page_size, 100),  # Ensure max limit
                    current_item=current_item,
                    order_direction=order_direction,
                    hierarchical_data=hierarchical_data
                )
                
                # Enhance the result with additional information
                enhanced_result = self._enhance_category_result(result)
                
                logger.info(f"✅ Successfully retrieved {len(result.get('data', []))} categories")
                return enhanced_result
                
        except Exception as e:
            logger.error(f"❌ Error retrieving categories: {e}")
            logger.error(f"🔍 Error type: {type(e).__name__}")
            raise Exception(f"Failed to retrieve categories: {str(e)}")
    
    def _validate_category_params(
        self, 
        page_size: int, 
        current_item: int, 
        order_direction: str
    ) -> None:
        """Validate category request parameters.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            order_direction: Sort order
        
        Raises:
            ValueError: If parameters are invalid
        """
        if page_size <= 0 or page_size > 100:
            raise ValueError("page_size must be between 1 and 100")
        
        if current_item < 0:
            raise ValueError("current_item must be non-negative")
        
        if order_direction not in ["Asc", "Desc"]:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
    
    def _enhance_category_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance category result with additional metadata.
        
        Args:
            result: Raw API result
        
        Returns:
            Enhanced result with metadata
        """
        categories = result.get('data', [])
        
        # Add metadata
        result['metadata'] = {
            'total_categories': len(categories),
            'has_subcategories': any(
                cat.get('hasChild', False) for cat in categories
            ),
            'category_names': [cat.get('categoryName', '') for cat in categories]
        }
        
        return result
    
    async def get_all_categories(
        self, 
        hierarchical_data: bool = False,
        max_pages: int = 10
    ) -> List[Dict[str, Any]]:
        """Get all categories by paginating through all pages.
        
        Args:
            hierarchical_data: Whether to return hierarchical structure
            max_pages: Maximum number of pages to fetch (safety limit)
        
        Returns:
            List of all categories
        
        Raises:
            Exception: If API request fails
        """
        logger.info(f"📂 Getting all categories (max {max_pages} pages)")
        
        all_categories = []
        current_item = 0
        page_size = 100  # Use maximum page size for efficiency
        page_count = 0
        
        try:
            while page_count < max_pages:
                result = await self.get_categories(
                    page_size=page_size,
                    current_item=current_item,
                    hierarchical_data=hierarchical_data
                )
                
                categories = result.get('data', [])
                if not categories:
                    break  # No more data
                
                all_categories.extend(categories)
                current_item += len(categories)
                page_count += 1
                
                # Check if we've reached the end
                total = result.get('total', 0)
                if current_item >= total:
                    break
                
                logger.info(f"📄 Fetched page {page_count}, total categories so far: {len(all_categories)}")
            
            logger.info(f"✅ Retrieved all {len(all_categories)} categories in {page_count} pages")
            return all_categories
            
        except Exception as e:
            logger.error(f"❌ Error retrieving all categories: {e}")
            raise Exception(f"Failed to retrieve all categories: {str(e)}")

    def build_category_tree(self, categories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build a hierarchical category tree from flat category data.

        Args:
            categories: List of category dictionaries

        Returns:
            Dictionary containing tree structure with metadata
        """
        # Create a mapping of category ID to category for quick lookup
        category_map = {cat.get('categoryId'): cat for cat in categories}

        # Initialize children lists
        for cat in categories:
            cat['children'] = []

        # Build the tree structure
        root_categories = []
        for cat in categories:
            parent_id = cat.get('parentId')
            if parent_id is None:
                root_categories.append(cat)
            elif parent_id in category_map:
                category_map[parent_id]['children'].append(cat)

        return {
            'root_categories': root_categories,
            'total_categories': len(categories),
            'tree_depth': self._calculate_tree_depth(root_categories)
        }

    def _calculate_tree_depth(self, categories: List[Dict[str, Any]]) -> int:
        """Calculate the maximum depth of the category tree.

        Args:
            categories: List of root categories with children

        Returns:
            Maximum depth of the tree
        """
        if not categories:
            return 0

        max_depth = 0
        for cat in categories:
            children = cat.get('children', [])
            if children:
                child_depth = self._calculate_tree_depth(children)
                max_depth = max(max_depth, 1 + child_depth)
            else:
                max_depth = max(max_depth, 1)

        return max_depth

    def search_categories(
        self,
        categories: List[Dict[str, Any]],
        search_term: str
    ) -> List[Dict[str, Any]]:
        """Search categories by name.

        Args:
            categories: List of category dictionaries to search
            search_term: Term to search for (case-insensitive)

        Returns:
            List of matching categories
        """
        if not search_term:
            return []

        search_term_lower = search_term.lower()
        results = []

        for cat in categories:
            category_name = cat.get('categoryName', '').lower()
            if search_term_lower in category_name:
                results.append(cat)

        return results

