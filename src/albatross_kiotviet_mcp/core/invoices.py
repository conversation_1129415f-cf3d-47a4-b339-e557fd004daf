"""Invoice business logic service.

This module contains all invoice-related business logic separated from MCP concerns.
It can be tested independently and reused across different interfaces.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..client import KiotVietAPIClient

logger = logging.getLogger(__name__)


class InvoiceService:
    """Service class for invoice-related business operations."""
    
    def __init__(self, client: KiotVietAPIClient):
        """Initialize the invoice service with a KiotViet API client.
        
        Args:
            client: Configured KiotViet API client instance
        """
        self.client = client
    
    async def get_invoices_by_date_range(
        self,
        from_date: str,
        to_date: Optional[str] = None,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        current_item: int = 0,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Get invoices by date range with calculated totals.
        
        Args:
            from_date: Start date in format "2025-07-19" or "2025-07-19T00:00:00"
            to_date: End date (optional, defaults to same day as from_date)
            include_payment: Whether to include payment information
            include_invoice_delivery: Whether to include delivery information
            current_item: Starting item index for pagination
            page_size: Number of items per page (max 100)
        
        Returns:
            Dictionary containing invoices data, pagination info, and calculated totals
        
        Raises:
            ValueError: If date format is invalid
            Exception: If API request fails
        """
        logger.info(f"🧾 Getting invoices by date range: from_date={from_date}, to_date={to_date}, page_size={page_size}")
        
        try:
            # Format and validate dates
            from_purchase_date, to_purchase_date = self._format_date_range(from_date, to_date)
            
            logger.info(f"📅 Formatted date range: {from_purchase_date} to {to_purchase_date}")
            
            # Make API request
            logger.info("🚀 Making API request to KiotViet invoices...")
            async with self.client:
                result = await self.client.get_invoices(
                    from_purchase_date=from_purchase_date,
                    to_purchase_date=to_purchase_date,
                    include_payment=include_payment,
                    include_invoice_delivery=include_invoice_delivery,
                    current_item=current_item,
                    page_size=page_size
                )
                
                # Process and enhance the result
                enhanced_result = self._enhance_invoice_result(
                    result, from_purchase_date, to_purchase_date
                )
                
                return enhanced_result
                
        except Exception as e:
            logger.error(f"❌ Error retrieving invoices: {e}")
            logger.error(f"🔍 Error type: {type(e).__name__}")
            raise Exception(f"Failed to retrieve invoices: {str(e)}")
    
    def _format_date_range(self, from_date: str, to_date: Optional[str] = None) -> tuple[str, str]:
        """Format and validate date range for API request.
        
        Args:
            from_date: Start date string
            to_date: End date string (optional)
        
        Returns:
            Tuple of (formatted_from_date, formatted_to_date)
        
        Raises:
            ValueError: If date format is invalid
        """
        # Format from_date to ISO format if needed
        if 'T' not in from_date:
            from_purchase_date = f"{from_date}T00:00:00"
        else:
            from_purchase_date = from_date
            
        # Handle to_date
        if to_date is None:
            # Use same day as from_date
            date_part = from_date.split('T')[0] if 'T' in from_date else from_date
            to_purchase_date = f"{date_part}T23:59:59"
        elif 'T' not in to_date:
            to_purchase_date = f"{to_date}T23:59:59"
        else:
            to_purchase_date = to_date
        
        # Basic validation
        try:
            datetime.fromisoformat(from_purchase_date.replace('T', ' '))
            datetime.fromisoformat(to_purchase_date.replace('T', ' '))
        except ValueError as e:
            raise ValueError(f"Invalid date format: {e}")
        
        return from_purchase_date, to_purchase_date
    
    def _enhance_invoice_result(
        self, 
        result: Dict[str, Any], 
        from_date: str, 
        to_date: str
    ) -> Dict[str, Any]:
        """Enhance invoice result with calculated totals and summary.
        
        Args:
            result: Raw API result
            from_date: Formatted start date
            to_date: Formatted end date
        
        Returns:
            Enhanced result with summary information
        """
        invoices = result.get('data', [])
        invoices_count = len(invoices)
        logger.info(f"✅ Successfully retrieved {invoices_count} invoices")
        
        # Calculate totals
        total_sum = 0
        total_payment_sum = 0
        
        for invoice in invoices:
            total = invoice.get('total', 0)
            total_payment = invoice.get('totalPayment', 0)
            
            if isinstance(total, (int, float)):
                total_sum += total
            if isinstance(total_payment, (int, float)):
                total_payment_sum += total_payment
        
        logger.info(f"💰 Calculated totals: total={total_sum:,.0f}, totalPayment={total_payment_sum:,.0f}")
        
        # Add summary to result
        result['summary'] = {
            'total_invoices': invoices_count,
            'total_sum': total_sum,
            'total_payment_sum': total_payment_sum,
            'date_range': {
                'from': from_date,
                'to': to_date
            }
        }
        
        return result
    
    async def get_invoice_by_id(self, invoice_id: str) -> Dict[str, Any]:
        """Get a specific invoice by ID.
        
        Args:
            invoice_id: The invoice ID to retrieve
        
        Returns:
            Invoice data
        
        Raises:
            Exception: If API request fails
        """
        logger.info(f"🧾 Getting invoice by ID: {invoice_id}")
        
        try:
            async with self.client:
                # This would need to be implemented in the client
                # For now, this is a placeholder for future functionality
                raise NotImplementedError("Get invoice by ID not yet implemented in KiotViet API client")
                
        except Exception as e:
            logger.error(f"❌ Error retrieving invoice {invoice_id}: {e}")
            raise Exception(f"Failed to retrieve invoice: {str(e)}")
    
    def calculate_invoice_statistics(self, invoices: list) -> Dict[str, Any]:
        """Calculate statistics for a list of invoices.
        
        Args:
            invoices: List of invoice dictionaries
        
        Returns:
            Dictionary containing various statistics
        """
        if not invoices:
            return {
                'count': 0,
                'total_amount': 0,
                'total_payment': 0,
                'average_amount': 0,
                'paid_invoices': 0,
                'unpaid_invoices': 0
            }
        
        total_amount = sum(inv.get('total', 0) for inv in invoices)
        total_payment = sum(inv.get('totalPayment', 0) for inv in invoices)
        paid_invoices = sum(1 for inv in invoices if inv.get('totalPayment', 0) > 0)
        
        return {
            'count': len(invoices),
            'total_amount': total_amount,
            'total_payment': total_payment,
            'average_amount': total_amount / len(invoices) if invoices else 0,
            'paid_invoices': paid_invoices,
            'unpaid_invoices': len(invoices) - paid_invoices
        }
