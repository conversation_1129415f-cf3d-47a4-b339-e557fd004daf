"""Logging infrastructure setup.

This module provides centralized logging configuration and setup.
"""

import os
import logging
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from ..config.settings import LoggingConfig


class LoggingSetup:
    """Handles logging configuration and setup."""
    
    def __init__(self, config: LoggingConfig):
        """Initialize logging setup with configuration."""
        self.config = config
        self._is_configured = False
    
    def setup_logging(self, project_root: Optional[str] = None) -> None:
        """Set up logging configuration.
        
        Args:
            project_root: Root directory of the project (for log file placement)
        """
        if self._is_configured:
            return
        
        # Determine project root
        if project_root is None:
            # Try to find project root automatically
            current_dir = Path(__file__).parent
            project_root = self._find_project_root(current_dir)
        
        # Create log directory
        log_dir = Path(project_root) / self.config.log_directory
        log_dir.mkdir(exist_ok=True)
        
        # Create handlers
        handlers = self._create_handlers(log_dir)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.config.log_level.upper()),
            format=self.config.log_format,
            handlers=handlers,
            force=True  # Override any existing configuration
        )
        
        self._is_configured = True
        
        # Log the setup completion
        logger = logging.getLogger(__name__)
        logger.info(f"Logging configured: level={self.config.log_level}, handlers={len(handlers)}")
        if self.config.log_to_file:
            log_file = log_dir / self._get_log_filename()
            logger.info(f"Log file: {log_file}")
    
    def _create_handlers(self, log_dir: Path) -> List[logging.Handler]:
        """Create logging handlers based on configuration.
        
        Args:
            log_dir: Directory for log files
            
        Returns:
            List of configured handlers
        """
        handlers = []
        
        # Console handler
        if self.config.log_to_console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(self.config.log_format))
            handlers.append(console_handler)
        
        # File handler
        if self.config.log_to_file:
            log_file = log_dir / self._get_log_filename()
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter(self.config.log_format))
            handlers.append(file_handler)
        
        return handlers
    
    def _get_log_filename(self) -> str:
        """Generate log filename with current date."""
        date_str = datetime.now().strftime("%Y%m%d")
        return f"kiotviet_mcp_{date_str}.log"
    
    def _find_project_root(self, start_path: Path) -> str:
        """Find project root by looking for pyproject.toml or similar files.
        
        Args:
            start_path: Starting directory to search from
            
        Returns:
            Path to project root
        """
        current = start_path.resolve()
        
        # Look for project indicators
        project_files = ['pyproject.toml', 'setup.py', '.git', 'requirements.txt']
        
        while current != current.parent:
            for project_file in project_files:
                if (current / project_file).exists():
                    return str(current)
            current = current.parent
        
        # Fallback to current directory
        return str(start_path)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the specified name.
        
        Args:
            name: Logger name
            
        Returns:
            Configured logger instance
        """
        if not self._is_configured:
            self.setup_logging()
        
        return logging.getLogger(name)


# Global logging setup instance
_logging_setup: Optional[LoggingSetup] = None


def get_logging_setup(config: Optional[LoggingConfig] = None) -> LoggingSetup:
    """Get the global logging setup instance.
    
    Args:
        config: Logging configuration (will create default if not provided)
        
    Returns:
        LoggingSetup instance
    """
    global _logging_setup
    
    if _logging_setup is None:
        if config is None:
            from ..config.settings import get_logging_config
            config = get_logging_config()
        _logging_setup = LoggingSetup(config)
    
    return _logging_setup


def setup_application_logging(project_root: Optional[str] = None) -> None:
    """Set up application logging (convenience function).
    
    Args:
        project_root: Root directory of the project
    """
    logging_setup = get_logging_setup()
    logging_setup.setup_logging(project_root)


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name (convenience function).
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    logging_setup = get_logging_setup()
    return logging_setup.get_logger(name)
