"""HTTP-specific exceptions.

These exceptions represent HTTP and network-related errors.
"""


class HTTPError(Exception):
    """Base exception for HTTP-related errors."""
    pass


class HTTPConnectionError(HTTPError):
    """Raised when HTTP connection fails."""
    pass


class HTTPTimeoutError(HTTPError):
    """Raised when HTTP request times out."""
    pass


class HTTPStatusError(HTTPError):
    """Raised when HTTP request returns an error status code."""
    
    def __init__(self, message: str, status_code: int, response_body: str = ""):
        """Initialize HTTP status error.
        
        Args:
            message: Error message
            status_code: HTTP status code
            response_body: Response body content
        """
        super().__init__(message)
        self.status_code = status_code
        self.response_body = response_body


class HTTPAuthenticationError(HTTPError):
    """Raised when HTTP authentication fails."""
    pass


class HTTPRateLimitError(HTTPError):
    """Raised when HTTP rate limit is exceeded."""
    pass
