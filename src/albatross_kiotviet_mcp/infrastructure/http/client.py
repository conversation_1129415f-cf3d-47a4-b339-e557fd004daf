"""HTTP client infrastructure with abstractions.

This module provides HTTP client abstractions and implementations.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Protocol
import httpx

from .exceptions import (
    HTTPConnectionError, 
    HTTPTimeoutError, 
    HTTPStatusError, 
    HTTPAuthenticationError,
    HTTPRateLimitError
)


class HTTPClientProtocol(Protocol):
    """Protocol for HTTP client implementations."""
    
    async def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Make an HTTP request.
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            json: JSON request body
            params: URL parameters
            
        Returns:
            Response data as dictionary
        """
        ...
    
    async def __aenter__(self):
        """Async context manager entry."""
        ...
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        ...


class BaseHTTPClient(ABC):
    """Base HTTP client with common functionality."""
    
    def __init__(
        self,
        base_url: str,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay_base: int = 2
    ):
        """Initialize HTTP client.
        
        Args:
            base_url: Base URL for requests
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            retry_delay_base: Base delay for exponential backoff
        """
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay_base = retry_delay_base
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    async def _make_raw_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """Make a raw HTTP request (to be implemented by subclasses).
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            json: JSON request body
            params: URL parameters
            
        Returns:
            Raw HTTP response
        """
        pass
    
    async def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Make an HTTP request with error handling and retries.
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            json: JSON request body
            params: URL parameters
            
        Returns:
            Response data as dictionary
            
        Raises:
            HTTPConnectionError: If connection fails
            HTTPTimeoutError: If request times out
            HTTPStatusError: If response has error status
            HTTPAuthenticationError: If authentication fails
            HTTPRateLimitError: If rate limit is exceeded
        """
        full_url = url if url.startswith('http') else f"{self.base_url}{url}"
        
        for attempt in range(self.max_retries):
            try:
                self._logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self._make_raw_request(
                    method=method,
                    url=full_url,
                    headers=headers,
                    json=json,
                    params=params
                )
                
                # Handle specific status codes
                if response.status_code == 401:
                    raise HTTPAuthenticationError(f"Authentication failed: {response.status_code}")
                elif response.status_code == 429:
                    if attempt < self.max_retries - 1:
                        await self._wait_for_retry(attempt)
                        continue
                    raise HTTPRateLimitError(f"Rate limit exceeded: {response.status_code}")
                elif response.status_code >= 400:
                    raise HTTPStatusError(
                        f"HTTP error {response.status_code}",
                        response.status_code,
                        response.text
                    )
                
                # Parse JSON response
                try:
                    result = response.json()
                    self._logger.debug(f"Request successful: {method} {url}")
                    return result
                except Exception as e:
                    raise HTTPStatusError(f"Failed to parse JSON response: {e}", response.status_code)
                
            except httpx.TimeoutException as e:
                self._logger.warning(f"Request timeout (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    raise HTTPTimeoutError(f"Request timeout after {self.max_retries} attempts") from e
                await self._wait_for_retry(attempt)
                
            except httpx.NetworkError as e:
                self._logger.warning(f"Network error (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    raise HTTPConnectionError(f"Network error after {self.max_retries} attempts") from e
                await self._wait_for_retry(attempt)
                
            except (HTTPAuthenticationError, HTTPRateLimitError, HTTPStatusError):
                # Don't retry these errors
                raise
                
            except Exception as e:
                self._logger.error(f"Unexpected error (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    raise HTTPConnectionError(f"Unexpected error after {self.max_retries} attempts: {e}") from e
                await self._wait_for_retry(attempt)
        
        # This should never be reached
        raise HTTPConnectionError(f"Request failed after {self.max_retries} attempts")
    
    async def _wait_for_retry(self, attempt: int) -> None:
        """Wait before retrying a failed request using exponential backoff.
        
        Args:
            attempt: Current attempt number (0-based)
        """
        delay = self.retry_delay_base ** attempt
        self._logger.debug(f"Waiting {delay} seconds before retry...")
        await asyncio.sleep(delay)


class HTTPXClient(BaseHTTPClient):
    """HTTP client implementation using httpx."""
    
    def __init__(self, *args, **kwargs):
        """Initialize httpx client."""
        super().__init__(*args, **kwargs)
        self._client: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=self.timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _make_raw_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """Make a raw HTTP request using httpx.
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            json: JSON request body
            params: URL parameters
            
        Returns:
            Raw httpx response
        """
        if not self._client:
            raise HTTPConnectionError("HTTP client not initialized. Use async context manager.")
        
        return await self._client.request(
            method=method,
            url=url,
            headers=headers,
            json=json,
            params=params
        )
