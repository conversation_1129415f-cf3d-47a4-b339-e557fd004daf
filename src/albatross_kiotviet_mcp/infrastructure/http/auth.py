"""Authentication infrastructure for HTTP clients.

This module provides authentication management and token handling.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Protocol

from .client import HTT<PERSON>lient<PERSON>rotocol
from .exceptions import HTTPAuthenticationError


class TokenManagerProtocol(Protocol):
    """Protocol for token manager implementations."""
    
    async def get_access_token(self) -> str:
        """Get a valid access token."""
        ...
    
    def invalidate_token(self) -> None:
        """Invalidate current token to force refresh."""
        ...


class BaseTokenManager(ABC):
    """Base token manager with common functionality."""
    
    def __init__(self, buffer_seconds: int = 300):
        """Initialize token manager.
        
        Args:
            buffer_seconds: Seconds before expiry to refresh token
        """
        self.buffer_seconds = buffer_seconds
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
        self._lock = asyncio.Lock()
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def get_access_token(self) -> str:
        """Get a valid access token, refreshing if necessary.
        
        Returns:
            Valid access token
            
        Raises:
            HTTPAuthenticationError: If token refresh fails
        """
        async with self._lock:
            if self._is_token_valid():
                return self._access_token
            
            self._logger.info("Access token expired or missing, refreshing...")
            await self._refresh_token()
            return self._access_token
    
    def invalidate_token(self) -> None:
        """Invalidate current token to force refresh on next request."""
        self._access_token = None
        self._token_expires_at = None
        self._logger.info("Token invalidated")
    
    def _is_token_valid(self) -> bool:
        """Check if current token is valid and not expiring soon.
        
        Returns:
            True if token is valid, False otherwise
        """
        if not self._access_token or not self._token_expires_at:
            return False
        
        # Check if token expires within buffer time
        buffer_time = timedelta(seconds=self.buffer_seconds)
        return datetime.now() + buffer_time < self._token_expires_at
    
    @abstractmethod
    async def _refresh_token(self) -> None:
        """Refresh the access token (to be implemented by subclasses).
        
        Raises:
            HTTPAuthenticationError: If token refresh fails
        """
        pass


class OAuth2ClientCredentialsTokenManager(BaseTokenManager):
    """Token manager for OAuth2 client credentials flow."""
    
    def __init__(
        self,
        auth_url: str,
        client_id: str,
        client_secret: str,
        scopes: str = "PublicApi.Access",
        buffer_seconds: int = 300,
        timeout: int = 30
    ):
        """Initialize OAuth2 token manager.
        
        Args:
            auth_url: OAuth2 token endpoint URL
            client_id: OAuth2 client ID
            client_secret: OAuth2 client secret
            scopes: OAuth2 scopes to request
            buffer_seconds: Seconds before expiry to refresh token
            timeout: Request timeout in seconds
        """
        super().__init__(buffer_seconds)
        self.auth_url = auth_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.scopes = scopes
        self.timeout = timeout
    
    async def _refresh_token(self) -> None:
        """Refresh the access token using OAuth2 client credentials flow.
        
        Raises:
            HTTPAuthenticationError: If token refresh fails
        """
        try:
            # Import here to avoid circular imports
            import httpx
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.auth_url,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    data={
                        "scopes": self.scopes,
                        "grant_type": "client_credentials",
                        "client_id": self.client_id,
                        "client_secret": self.client_secret
                    }
                )
                
                if response.status_code != 200:
                    raise HTTPAuthenticationError(
                        f"Token refresh failed with status {response.status_code}: {response.text}"
                    )
                
                token_data = response.json()
                
                if "access_token" not in token_data:
                    raise HTTPAuthenticationError("No access_token in response")
                
                self._access_token = token_data["access_token"]
                expires_in = token_data.get("expires_in", 3600)  # Default 1 hour
                self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                self._logger.info(f"Token refreshed successfully, expires at: {self._token_expires_at}")
                
        except httpx.HTTPError as e:
            self._logger.error(f"HTTP error during token refresh: {e}")
            raise HTTPAuthenticationError(f"Token refresh failed: {e}") from e
        except KeyError as e:
            self._logger.error(f"Invalid token response format: {e}")
            raise HTTPAuthenticationError(f"Invalid token response: {e}") from e
        except Exception as e:
            self._logger.error(f"Unexpected error during token refresh: {e}")
            raise HTTPAuthenticationError(f"Token refresh failed: {e}") from e


class AuthenticatedHTTPClient:
    """HTTP client wrapper that adds authentication headers."""
    
    def __init__(self, http_client: HTTPClientProtocol, token_manager: TokenManagerProtocol):
        """Initialize authenticated HTTP client.
        
        Args:
            http_client: Base HTTP client
            token_manager: Token manager for authentication
        """
        self.http_client = http_client
        self.token_manager = token_manager
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Make an authenticated HTTP request.
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers (auth header will be added)
            json: JSON request body
            params: URL parameters
            **kwargs: Additional arguments
            
        Returns:
            Response data as dictionary
        """
        # Get authentication headers
        auth_headers = await self._get_auth_headers()
        
        # Merge with provided headers
        if headers:
            auth_headers.update(headers)
        
        return await self.http_client.request(
            method=method,
            url=url,
            headers=auth_headers,
            json=json,
            params=params,
            **kwargs
        )
    
    async def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers.
        
        Returns:
            Dictionary containing authentication headers
            
        Raises:
            HTTPAuthenticationError: If authentication fails
        """
        try:
            access_token = await self.token_manager.get_access_token()
            return {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        except Exception as e:
            self._logger.error(f"Failed to get authentication headers: {e}")
            raise HTTPAuthenticationError(f"Authentication failed: {e}") from e
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.http_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.http_client.__aexit__(exc_type, exc_val, exc_tb)
