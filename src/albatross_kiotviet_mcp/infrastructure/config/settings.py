"""Configuration management for KiotViet MCP Server.

This module provides centralized configuration management using Pydantic settings.
"""

import os
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class KiotVietConfig(BaseSettings):
    """Configuration for KiotViet API integration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    # KiotViet API credentials
    client_id: str = Field(alias="KIOTVIET_CLIENT_ID")
    client_secret: str = Field(alias="KIOTVIET_CLIENT_SECRET")
    retailer: str = Field(alias="KIOTVIET_RETAILER")
    
    # API endpoints
    auth_url: str = Field(
        default="https://id.kiotviet.vn/connect/token",
        alias="KIOTVIET_AUTH_URL"
    )
    api_base_url: str = Field(
        default="https://public.kiotapi.com",
        alias="KIOTVIET_API_BASE_URL"
    )
    
    # Token settings
    token_buffer_seconds: int = Field(
        default=300,  # Refresh token 5 minutes before expiry
        alias="KIOTVIET_TOKEN_BUFFER_SECONDS"
    )
    
    # Request settings
    request_timeout: int = Field(default=30, alias="KIOTVIET_REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, alias="KIOTVIET_MAX_RETRIES")


class LoggingConfig(BaseSettings):
    """Configuration for logging."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    # Logging settings
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        alias="LOG_FORMAT"
    )
    log_to_file: bool = Field(default=True, alias="LOG_TO_FILE")
    log_to_console: bool = Field(default=True, alias="LOG_TO_CONSOLE")
    log_directory: str = Field(default="logs", alias="LOG_DIRECTORY")


class ServerConfig(BaseSettings):
    """Configuration for the MCP server."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8"
    )
    
    # Server settings
    server_name: str = Field(default="KiotVietMCPServer", alias="SERVER_NAME")
    debug_mode: bool = Field(default=False, alias="DEBUG_MODE")


class AppConfig:
    """Main application configuration that combines all config sections."""
    
    def __init__(self):
        """Initialize all configuration sections."""
        self.kiotviet = KiotVietConfig()
        self.logging = LoggingConfig()
        self.server = ServerConfig()
    
    def validate_all(self) -> None:
        """Validate all configuration sections."""
        # This will raise validation errors if any required fields are missing
        self.kiotviet.model_validate(self.kiotviet.model_dump())
        self.logging.model_validate(self.logging.model_dump())
        self.server.model_validate(self.server.model_dump())


# Global configuration instance
_app_config: Optional[AppConfig] = None


def get_app_config() -> AppConfig:
    """Get the global application configuration instance."""
    global _app_config
    if _app_config is None:
        _app_config = AppConfig()
    return _app_config


def get_kiotviet_config() -> KiotVietConfig:
    """Get KiotViet configuration (for backward compatibility)."""
    return get_app_config().kiotviet


def get_logging_config() -> LoggingConfig:
    """Get logging configuration."""
    return get_app_config().logging


def get_server_config() -> ServerConfig:
    """Get server configuration."""
    return get_app_config().server
