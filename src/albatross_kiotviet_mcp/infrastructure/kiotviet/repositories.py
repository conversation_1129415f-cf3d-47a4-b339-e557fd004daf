"""KiotViet repository implementations.

This module provides concrete implementations of repository interfaces
for accessing KiotViet API data.
"""

import logging
from typing import List, Dict, Any, Optional

from ...domain.entities.category import Category
from ...domain.entities.invoice import Invoice
from .client import KiotVietAPIClient


class KiotVietCategoryRepository:
    """Repository for accessing category data from KiotViet API."""
    
    def __init__(self, client: KiotVietAPIClient):
        """Initialize category repository.
        
        Args:
            client: KiotViet API client
        """
        self.client = client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get categories from KiotViet API.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            order_direction: Sort order
            hierarchical_data: Whether to return hierarchical structure
            
        Returns:
            Raw API response
        """
        async with self.client:
            return await self.client.get_categories(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )
    
    async def get_all_categories(
        self,
        hierarchical_data: bool = False,
        max_pages: int = 10
    ) -> List[Dict[str, Any]]:
        """Get all categories by paginating through all pages.
        
        Args:
            hierarchical_data: Whether to return hierarchical structure
            max_pages: Maximum number of pages to fetch
            
        Returns:
            List of all category data
        """
        all_categories = []
        current_item = 0
        page_size = 100  # Use maximum page size for efficiency
        page_count = 0
        
        async with self.client:
            while page_count < max_pages:
                result = await self.client.get_categories(
                    page_size=page_size,
                    current_item=current_item,
                    order_direction="Asc",
                    hierarchical_data=hierarchical_data
                )
                
                categories = result.get('data', [])
                if not categories:
                    break  # No more data
                
                all_categories.extend(categories)
                current_item += len(categories)
                page_count += 1
                
                # Check if we've reached the end
                total = result.get('total', 0)
                if current_item >= total:
                    break
                
                self._logger.debug(f"Fetched page {page_count}, total categories so far: {len(all_categories)}")
        
        self._logger.info(f"Retrieved all {len(all_categories)} categories in {page_count} pages")
        return all_categories


class KiotVietInvoiceRepository:
    """Repository for accessing invoice data from KiotViet API."""
    
    def __init__(self, client: KiotVietAPIClient):
        """Initialize invoice repository.
        
        Args:
            client: KiotViet API client
        """
        self.client = client
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def get_invoices_by_date_range(
        self,
        from_purchase_date: str,
        to_purchase_date: str,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        current_item: int = 0,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Get invoices by date range from KiotViet API.
        
        Args:
            from_purchase_date: Start date in ISO format
            to_purchase_date: End date in ISO format
            include_payment: Whether to include payment information
            include_invoice_delivery: Whether to include delivery information
            current_item: Starting item index
            page_size: Number of items per page
            
        Returns:
            Raw API response
        """
        async with self.client:
            return await self.client.get_invoices(
                from_purchase_date=from_purchase_date,
                to_purchase_date=to_purchase_date,
                include_payment=include_payment,
                include_invoice_delivery=include_invoice_delivery,
                current_item=current_item,
                page_size=page_size
            )
    
    async def get_all_invoices_by_date_range(
        self,
        from_purchase_date: str,
        to_purchase_date: str,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        max_pages: int = 10
    ) -> List[Dict[str, Any]]:
        """Get all invoices by date range by paginating through all pages.
        
        Args:
            from_purchase_date: Start date in ISO format
            to_purchase_date: End date in ISO format
            include_payment: Whether to include payment information
            include_invoice_delivery: Whether to include delivery information
            max_pages: Maximum number of pages to fetch
            
        Returns:
            List of all invoice data
        """
        all_invoices = []
        current_item = 0
        page_size = 100  # Use maximum page size for efficiency
        page_count = 0
        
        async with self.client:
            while page_count < max_pages:
                result = await self.client.get_invoices(
                    from_purchase_date=from_purchase_date,
                    to_purchase_date=to_purchase_date,
                    include_payment=include_payment,
                    include_invoice_delivery=include_invoice_delivery,
                    current_item=current_item,
                    page_size=page_size
                )
                
                invoices = result.get('data', [])
                if not invoices:
                    break  # No more data
                
                all_invoices.extend(invoices)
                current_item += len(invoices)
                page_count += 1
                
                # Check if we've reached the end
                total = result.get('total', 0)
                if current_item >= total:
                    break
                
                self._logger.debug(f"Fetched page {page_count}, total invoices so far: {len(all_invoices)}")
        
        self._logger.info(f"Retrieved all {len(all_invoices)} invoices in {page_count} pages")
        return all_invoices
