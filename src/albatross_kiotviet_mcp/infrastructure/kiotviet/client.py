"""KiotViet API client implementation.

This module provides the concrete implementation of the KiotViet API client
using the infrastructure abstractions.
"""

import logging
from typing import Dict, Any, Optional

from ..config.settings import KiotVietConfig
from ..http.client import HTTPXClient
from ..http.auth import OAuth2ClientCredentialsTokenManager, AuthenticatedHTTPClient
from ..http.exceptions import HTTPAuthenticationError, HTTPStatusError


class KiotVietAPIClient:
    """KiotViet API client implementation using infrastructure abstractions."""
    
    def __init__(self, config: KiotVietConfig):
        """Initialize KiotViet API client.
        
        Args:
            config: KiotViet configuration
        """
        self.config = config
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Create HTTP client
        self._http_client = HTTPXClient(
            base_url=config.api_base_url,
            timeout=config.request_timeout,
            max_retries=config.max_retries
        )
        
        # Create token manager
        self._token_manager = OAuth2ClientCredentialsTokenManager(
            auth_url=config.auth_url,
            client_id=config.client_id,
            client_secret=config.client_secret,
            buffer_seconds=config.token_buffer_seconds,
            timeout=config.request_timeout
        )
        
        # Create authenticated client
        self._authenticated_client = AuthenticatedHTTPClient(
            self._http_client,
            self._token_manager
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._authenticated_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._authenticated_client.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.
        
        Args:
            page_size: Number of items per page (1-100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure
            
        Returns:
            API response containing categories data
        """
        # Build request data
        data = {
            "pageSize": min(page_size, 100),
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierachicalData": hierarchical_data  # Note: API uses this spelling
        }
        
        # Add retailer header
        headers = {"Retailer": self.config.retailer}
        
        self._logger.info(
            f"Fetching categories: page_size={page_size}, current_item={current_item}, "
            f"order={order_direction}, hierarchical={hierarchical_data}"
        )
        
        try:
            response = await self._authenticated_client.request(
                method="GET",
                url="/categories",
                headers=headers,
                json=data
            )
            
            self._logger.debug(f"Categories request successful")
            return response
            
        except HTTPAuthenticationError as e:
            # Try to invalidate token and let the retry mechanism handle it
            self._token_manager.invalidate_token()
            self._logger.warning(f"Authentication error, token invalidated: {e}")
            raise
        except Exception as e:
            self._logger.error(f"Categories request failed: {e}")
            raise
    
    async def get_invoices(
        self,
        from_purchase_date: str,
        to_purchase_date: str,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        current_item: int = 0,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Get invoices from KiotViet API by date range.
        
        Args:
            from_purchase_date: Start date in ISO format
            to_purchase_date: End date in ISO format
            include_payment: Whether to include payment information
            include_invoice_delivery: Whether to include delivery information
            current_item: Starting item index for pagination
            page_size: Number of items per page (1-100)
            
        Returns:
            API response containing invoices data
        """
        # Build request data
        data = {
            "includePayment": include_payment,
            "includeInvoiceDelivery": include_invoice_delivery,
            "currentItem": current_item,
            "pageSize": min(page_size, 100),
            "fromPurchaseDate": from_purchase_date,
            "toPurchaseDate": to_purchase_date
        }
        
        # Add retailer header
        headers = {"Retailer": self.config.retailer}
        
        self._logger.info(
            f"Fetching invoices: date_range={from_purchase_date} to {to_purchase_date}, "
            f"page_size={page_size}, current_item={current_item}"
        )
        
        try:
            response = await self._authenticated_client.request(
                method="GET",
                url="/invoices",
                headers=headers,
                json=data
            )
            
            self._logger.debug(f"Invoices request successful")
            return response
            
        except HTTPAuthenticationError as e:
            # Try to invalidate token and let the retry mechanism handle it
            self._token_manager.invalidate_token()
            self._logger.warning(f"Authentication error, token invalidated: {e}")
            raise
        except Exception as e:
            self._logger.error(f"Invoices request failed: {e}")
            raise
