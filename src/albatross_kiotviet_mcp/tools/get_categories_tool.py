"""MCP Tool: get_categories

This module defines the get_categories MCP tool that retrieves product categories
from KiotViet API with pagination support.
"""

import logging
from typing import Dict, Any
from fastmcp import FastMCP

from ..core.categories import CategoryService
from ..client import KiotVietAPIClient
from ..config import get_config

logger = logging.getLogger(__name__)

# Global service instance (lazy-loaded)
_category_service: CategoryService = None


def get_category_service() -> CategoryService:
    """Get or create category service instance.
    
    Returns:
        CategoryService: Configured category service instance
    """
    global _category_service
    
    if _category_service is None:
        config = get_config()
        client = KiotVietAPIClient(config)
        _category_service = CategoryService(client)
    
    return _category_service


def register_get_categories_tool(server: FastMCP) -> None:
    """Register the get_categories MCP tool with the server.
    
    Args:
        server: FastMCP server instance to register the tool with
    """
    
    @server.tool(
        name="get_categories",
        description="Get product categories from KiotViet API with pagination support"
    )
    async def get_categories(
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.
        
        This tool retrieves product categories with support for pagination,
        sorting, and hierarchical data structure.
        
        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)
        
        Returns:
            Dictionary containing:
                - data: List of category records
                - metadata: Additional information about categories
                - pagination info: total, pageSize, etc.
        
        Raises:
            ValueError: If parameters are invalid
            Exception: If API request fails
        """
        try:
            service = get_category_service()
            result = await service.get_categories(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )
            
            categories_count = len(result.get('data', []))
            logger.info(f"✅ get_categories completed: {categories_count} categories retrieved")
            return result
            
        except Exception as e:
            logger.error(f"❌ get_categories failed: {e}")
            raise
    
    logger.info("✅ get_categories tool registered successfully")


def get_category_service_for_testing() -> CategoryService:
    """Get category service instance for testing purposes.
    
    This function provides direct access to the service for unit testing,
    bypassing the MCP tool wrapper.
    
    Returns:
        CategoryService: Service instance for testing
    """
    return get_category_service()
