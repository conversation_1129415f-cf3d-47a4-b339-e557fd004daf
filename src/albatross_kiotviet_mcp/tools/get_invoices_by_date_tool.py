"""MCP Tool: get_invoices_by_date

This module defines the get_invoices_by_date MCP tool that retrieves invoices
from KiotViet API by date range with total calculations.
"""

import logging
from typing import Dict, Any
from fastmcp import FastMCP

from ..core.invoices import InvoiceService
from ..client import KiotVietAPIClient
from ..config import get_config

logger = logging.getLogger(__name__)

# Global service instance (lazy-loaded)
_invoice_service: InvoiceService = None


def get_invoice_service() -> InvoiceService:
    """Get or create invoice service instance.
    
    Returns:
        InvoiceService: Configured invoice service instance
    """
    global _invoice_service
    
    if _invoice_service is None:
        config = get_config()
        client = KiotVietAPIClient(config)
        _invoice_service = InvoiceService(client)
    
    return _invoice_service


def register_get_invoices_by_date_tool(server: FastMCP) -> None:
    """Register the get_invoices_by_date MCP tool with the server.
    
    Args:
        server: FastMCP server instance to register the tool with
    """
    
    @server.tool(
        name="get_invoices_by_date",
        description="Get invoices from KiotViet API by date range with total calculations"
    )
    async def get_invoices_by_date(
        from_date: str,
        to_date: str = None,
        include_payment: bool = True,
        include_invoice_delivery: bool = True,
        current_item: int = 0,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Get invoices from KiotViet API by date range and calculate totals.
        
        This tool retrieves invoices for a specified date range and automatically
        calculates summary statistics including total amounts and payment totals.
        
        Args:
            from_date: Start date in format "2025-07-19" or "2025-07-19T00:00:00"
            to_date: End date (optional, defaults to same day as from_date)
            include_payment: Whether to include payment information (default: True)
            include_invoice_delivery: Whether to include delivery information (default: True)
            current_item: Starting item index for pagination (default: 0)
            page_size: Number of items per page (default: 50, max: 100)
        
        Returns:
            Dictionary containing:
                - data: List of invoice records
                - summary: Calculated totals and statistics
                - pagination info: total, pageSize, etc.
        
        Raises:
            ValueError: If date format is invalid
            Exception: If API request fails
        """
        try:
            service = get_invoice_service()
            result = await service.get_invoices_by_date_range(
                from_date=from_date,
                to_date=to_date,
                include_payment=include_payment,
                include_invoice_delivery=include_invoice_delivery,
                current_item=current_item,
                page_size=page_size
            )
            
            logger.info(f"✅ get_invoices_by_date completed: {result['summary']['total_invoices']} invoices")
            return result
            
        except Exception as e:
            logger.error(f"❌ get_invoices_by_date failed: {e}")
            raise
    
    logger.info("✅ get_invoices_by_date tool registered successfully")


def get_invoice_service_for_testing() -> InvoiceService:
    """Get invoice service instance for testing purposes.
    
    This function provides direct access to the service for unit testing,
    bypassing the MCP tool wrapper.
    
    Returns:
        InvoiceService: Service instance for testing
    """
    return get_invoice_service()
