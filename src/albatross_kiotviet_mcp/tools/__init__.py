"""MCP Tools for KiotViet API.

This module contains all MCP tool definitions that wrap the core business logic.
Each tool is defined in its own dedicated file for better modularity and maintainability.
Tools are thin wrappers that handle MCP-specific concerns like parameter validation
and response formatting, while delegating actual work to the core services.
"""

# Import individual tool registration functions (only existing ones)
from .get_invoices_by_date_tool import register_get_invoices_by_date_tool
from .get_invoice_statistics_tool import register_get_invoice_statistics_tool
from .get_categories_tool import register_get_categories_tool

# Import testing functions for backward compatibility
from .get_invoices_by_date_tool import get_invoice_service_for_testing as get_invoice_service_for_testing_invoices
from .get_categories_tool import get_category_service_for_testing as get_category_service_for_testing_categories

__all__ = [
    # Individual tool registration functions
    "register_get_invoices_by_date_tool",
    "register_get_invoice_statistics_tool",
    "register_get_categories_tool",

    # Testing functions for backward compatibility
    "get_invoice_service_for_testing_invoices",
    "get_category_service_for_testing_categories",
]


def register_all_tools(server):
    """Register all MCP tools with the server.

    This is a convenience function that registers all available tools.

    Args:
        server: FastMCP server instance to register tools with
    """
    register_get_invoices_by_date_tool(server)
    register_get_invoice_statistics_tool(server)
    register_get_categories_tool(server)
