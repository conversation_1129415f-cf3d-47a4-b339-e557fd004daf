"""MCP Tool: get_invoice_statistics

This module defines the get_invoice_statistics MCP tool that calculates
comprehensive statistics for invoices in a date range.
"""

import logging
from typing import Dict, Any
from fastmcp import FastMCP

from ..core.invoices import InvoiceService
from ..client import KiotVietAPIClient
from ..config import get_config

logger = logging.getLogger(__name__)

# Global service instance (lazy-loaded)
_invoice_service: InvoiceService = None


def get_invoice_service() -> InvoiceService:
    """Get or create invoice service instance.
    
    Returns:
        InvoiceService: Configured invoice service instance
    """
    global _invoice_service
    
    if _invoice_service is None:
        config = get_config()
        client = KiotVietAPIClient(config)
        _invoice_service = InvoiceService(client)
    
    return _invoice_service


def register_get_invoice_statistics_tool(server: FastMCP) -> None:
    """Register the get_invoice_statistics MCP tool with the server.
    
    Args:
        server: FastMCP server instance to register the tool with
    """
    
    @server.tool(
        name="get_invoice_statistics",
        description="Calculate statistics for invoices in a date range"
    )
    async def get_invoice_statistics(
        from_date: str,
        to_date: str = None,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """Calculate comprehensive statistics for invoices in a date range.
        
        This tool retrieves invoices for a specified date range and calculates
        detailed statistics including averages, payment ratios, and more.
        
        Args:
            from_date: Start date in format "2025-07-19" or "2025-07-19T00:00:00"
            to_date: End date (optional, defaults to same day as from_date)
            page_size: Number of items per page for data retrieval (default: 100)
        
        Returns:
            Dictionary containing:
                - date_range: The actual date range used
                - basic_summary: Basic totals and counts
                - detailed_statistics: Advanced statistics (averages, ratios, etc.)
                - sample_invoices: First 5 invoices as examples
        
        Raises:
            ValueError: If date format is invalid
            Exception: If API request fails
        """
        try:
            service = get_invoice_service()
            
            # Get the invoice data
            result = await service.get_invoices_by_date_range(
                from_date=from_date,
                to_date=to_date,
                include_payment=True,
                include_invoice_delivery=False,
                current_item=0,
                page_size=page_size
            )
            
            # Calculate additional statistics
            invoices = result.get('data', [])
            statistics = service.calculate_invoice_statistics(invoices)
            
            # Combine with existing summary
            response = {
                'date_range': result['summary']['date_range'],
                'basic_summary': result['summary'],
                'detailed_statistics': statistics,
                'sample_invoices': invoices[:5]  # Include first 5 invoices as samples
            }
            
            logger.info(f"✅ get_invoice_statistics completed: {statistics['count']} invoices analyzed")
            return response
            
        except Exception as e:
            logger.error(f"❌ get_invoice_statistics failed: {e}")
            raise
    
    logger.info("✅ get_invoice_statistics tool registered successfully")


def get_invoice_service_for_testing() -> InvoiceService:
    """Get invoice service instance for testing purposes.
    
    This function provides direct access to the service for unit testing,
    bypassing the MCP tool wrapper.
    
    Returns:
        InvoiceService: Service instance for testing
    """
    return get_invoice_service()
